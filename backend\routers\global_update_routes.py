# backend/routers/global_update_routes.py
"""
Rotas para Global Update - Atualização global do MCP em ciclo estruturado.
Implementa a arquitetura TTS conforme especificação.
"""

from fastapi import APIRouter, HTTPException, Body
from typing import Dict, Any, List
import logging
from datetime import datetime

# Importa MCP Data Manager
from backend.mcp.data_manager import MCPDataManager

router = APIRouter(prefix="/global-update", tags=["global-update"])
logger = logging.getLogger(__name__)

# Instância do MCP Data Manager
mcp_data_manager = MCPDataManager()

# Módulos ativos conforme especificação
ACTIVE_MODULES = [
    'transformerInputs',  # Dados básicos
    'losses',
    'impulse', 
    'appliedVoltage',
    'inducedVoltage',
    'shortCircuit',
    'temperatureRise',
    'dielectricAnalysis'
]

@router.post("/")
async def trigger_global_update(data: Dict[str, Any] = Body(...)):
    """
    Dispara atualização global do MCP em ciclo estruturado.
    
    Ciclo lógico:
    1. Dados Básicos → Propagam para todos os módulos
    2. Services → Processam dados + cálculos
    3. MCP → Atualiza stores em sequência estruturada
    4. Templates → Recebem dados atualizados
    """
    try:
        triggered_by = data.get('triggeredBy', 'unknown')
        timestamp = data.get('timestamp', datetime.now().isoformat())
        
        logger.info(f"Global Update disparado por: {triggered_by} em {timestamp}")
        
        # 1. OBTÉM DADOS BÁSICOS (fonte da verdade)
        basic_data = mcp_data_manager.get_data('transformerInputs')
        if not basic_data:
            logger.warning("Nenhum dado básico encontrado para propagação")
            basic_data = {}
        
        basic_form_data = basic_data.get('formData', {})
        logger.info(f"Dados básicos obtidos: {len(basic_form_data)} campos")
        
        # 2. CICLO ESTRUTURADO DE ATUALIZAÇÃO
        update_results = {}
        
        for module_id in ACTIVE_MODULES:
            if module_id == 'transformerInputs':
                # Dados básicos já estão atualizados
                update_results[module_id] = {
                    'status': 'source',
                    'message': 'Fonte dos dados básicos'
                }
                continue
            
            try:
                # Obtém dados específicos do módulo
                module_data = mcp_data_manager.get_data(module_id)
                module_inputs = module_data.get('inputs', {}) if module_data else {}
                
                # Combina dados básicos com inputs específicos
                combined_data = {
                    'basicData': basic_form_data,
                    'moduleData': module_inputs
                }
                
                # Atualiza store do módulo com dados básicos propagados
                updated_store_data = {
                    'inputs': module_inputs,
                    'basicData': basic_form_data,  # Propagação dos dados básicos
                    'results': module_data.get('results', {}) if module_data else {},
                    'lastGlobalUpdate': timestamp
                }
                
                success = mcp_data_manager.patch_data(module_id, updated_store_data)
                
                if success:
                    update_results[module_id] = {
                        'status': 'updated',
                        'message': 'Dados básicos propagados com sucesso'
                    }
                    logger.info(f"Módulo {module_id} atualizado no ciclo global")
                else:
                    update_results[module_id] = {
                        'status': 'error',
                        'message': 'Erro ao atualizar store'
                    }
                    logger.error(f"Erro ao atualizar módulo {module_id}")
                
            except Exception as e:
                update_results[module_id] = {
                    'status': 'error',
                    'message': f'Erro: {str(e)}'
                }
                logger.error(f"Erro no módulo {module_id}: {str(e)}")
        
        # 3. REGISTRA ATUALIZAÇÃO GLOBAL
        global_update_record = {
            'triggeredBy': triggered_by,
            'timestamp': timestamp,
            'modulesUpdated': len([r for r in update_results.values() if r['status'] == 'updated']),
            'modulesWithError': len([r for r in update_results.values() if r['status'] == 'error']),
            'results': update_results
        }
        
        # Armazena registro da atualização global
        mcp_data_manager.patch_data('globalUpdateLog', {
            'lastUpdate': global_update_record,
            'history': mcp_data_manager.get_data('globalUpdateLog', {}).get('history', [])[-9:] + [global_update_record]
        })
        
        logger.info(f"Global Update concluído: {global_update_record['modulesUpdated']} módulos atualizados")
        
        return {
            'success': True,
            'triggeredBy': triggered_by,
            'timestamp': timestamp,
            'modulesUpdated': global_update_record['modulesUpdated'],
            'modulesWithError': global_update_record['modulesWithError'],
            'results': update_results,
            'message': 'Atualização global concluída com sucesso'
        }
        
    except Exception as e:
        logger.error(f"Erro no Global Update: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro na atualização global: {str(e)}"
        )

@router.get("/status")
async def get_global_update_status():
    """
    Obtém status da última atualização global.
    """
    try:
        log_data = mcp_data_manager.get_data('globalUpdateLog')
        
        if not log_data or 'lastUpdate' not in log_data:
            return {
                'success': True,
                'status': 'never_updated',
                'message': 'Nenhuma atualização global registrada'
            }
        
        last_update = log_data['lastUpdate']
        
        return {
            'success': True,
            'status': 'updated',
            'lastUpdate': last_update,
            'message': 'Status da última atualização global'
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter status do Global Update: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )

@router.get("/history")
async def get_global_update_history():
    """
    Obtém histórico das atualizações globais.
    """
    try:
        log_data = mcp_data_manager.get_data('globalUpdateLog')
        
        if not log_data:
            return {
                'success': True,
                'history': [],
                'message': 'Nenhum histórico encontrado'
            }
        
        return {
            'success': True,
            'history': log_data.get('history', []),
            'message': 'Histórico de atualizações globais'
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter histórico do Global Update: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )
