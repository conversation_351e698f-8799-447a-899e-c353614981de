# backend/routers/transformer_routes.py
import sys
import pathlib
from fastapi import APIRouter, HTTPException, Body
from typing import Dict, Any, Optional
from pydantic import BaseModel

# Ajusta o path para permitir importações corretas
current_file = pathlib.Path(__file__).absolute()
current_dir = current_file.parent
backend_dir = current_dir.parent
root_dir = backend_dir.parent

if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

try:
    from ..mcp import MCPDataManager
    from ..services import transformer_service
except ImportError:
    try:
        from backend.mcp import MCPDataManager
        from backend.services import transformer_service
    except ImportError:
        from mcp import MCPDataManager
        from services import transformer_service

# Placeholder para a instância do MCP (será definida por main.py)
mcp_data_manager = None

router = APIRouter(prefix="/api/transformer", tags=["transformer"])

class TransformerInputsData(BaseModel):
    # Campos do formulário transformer_inputs.html
    potencia_mva: Optional[float] = None
    frequencia: Optional[float] = None
    tipo_transformador: Optional[str] = None
    grupo_ligacao: Optional[str] = None
    liquido_isolante: Optional[str] = None
    tipo_isolamento: Optional[str] = None
    norma_iso: Optional[str] = None
    elevacao_oleo_topo: Optional[float] = None
    elevacao_enrol: Optional[float] = None
    peso_parte_ativa: Optional[float] = None
    peso_tanque_acessorios: Optional[float] = None
    peso_oleo: Optional[float] = None
    peso_total: Optional[float] = None
    peso_adicional: Optional[float] = None
    tensao_at: Optional[float] = None
    classe_tensao_at: Optional[float] = None
    impedancia: Optional[float] = None
    nbi_at: Optional[float] = None
    sil_at: Optional[float] = None
    conexao_at: Optional[str] = None
    tensao_bucha_neutro_at: Optional[float] = None
    nbi_neutro_at: Optional[float] = None
    sil_neutro_at: Optional[float] = None
    tensao_at_tap_maior: Optional[float] = None
    tensao_at_tap_menor: Optional[float] = None
    impedancia_tap_maior: Optional[float] = None
    impedancia_tap_menor: Optional[float] = None
    teste_tensao_aplicada_at: Optional[float] = None
    teste_tensao_induzida_at: Optional[float] = None
    tensao_bt: Optional[float] = None
    classe_tensao_bt: Optional[float] = None
    nbi_bt: Optional[float] = None
    sil_bt: Optional[float] = None
    conexao_bt: Optional[str] = None
    tensao_bucha_neutro_bt: Optional[float] = None
    nbi_neutro_bt: Optional[float] = None
    sil_neutro_bt: Optional[float] = None
    teste_tensao_aplicada_bt: Optional[float] = None
    tensao_terciario: Optional[float] = None
    classe_tensao_terciario: Optional[float] = None
    nbi_terciario: Optional[float] = None
    sil_terciario: Optional[float] = None
    conexao_terciario: Optional[str] = None
    tensao_bucha_neutro_terciario: Optional[float] = None
    nbi_neutro_terciario: Optional[float] = None
    sil_neutro_terciario: Optional[float] = None
    teste_tensao_aplicada_terciario: Optional[float] = None

@router.post("/inputs")
async def update_transformer_inputs(data: TransformerInputsData = Body(...)):
    """
    Recebe os dados de entrada do formulário do transformador,
    calcula os valores derivados e os persiste no store 'transformer_inputs'.
    """
    try:
        # Converte o Pydantic Model para um dicionário
        input_data_dict = data.dict(exclude_unset=True)
        
        # Calcula os dados derivados (correntes nominais, etc.)
        calculated_data = transformer_service.calculate_and_process_transformer_data(input_data_dict)
        
        # Combina os dados de entrada com os dados calculados
        final_data = {**input_data_dict, **calculated_data}
        
        # Persiste os dados combinados no store 'transformer_inputs'
        success = mcp_data_manager.patch_data('transformer_inputs', {"formData": final_data})
        
        if success:
            return {
                "status": "success",
                "message": "Dados do transformador atualizados e calculados com sucesso.",
                "updated_data": final_data
            }
        else:
            raise HTTPException(status_code=500, detail="Falha ao persistir dados do transformador.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao processar dados do transformador: {str(e)}")
