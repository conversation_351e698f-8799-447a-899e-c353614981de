// public/scripts/dielectric_analysis.js - ATUALIZADO (Combinado com abas)

import { loadAndPopulateTransformerInfo } from './common_module.js';

document.addEventListener('DOMContentLoaded', async function() {
    console.log('<PERSON><PERSON><PERSON><PERSON>létrica (com abas) carregado e pronto para interatividade.');

    // ID do placeholder para o painel de informações do transformador
    const transformerInfoPlaceholderId = 'transformer-info-dieletric-page';
    await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId); // Carrega o painel no topo

    // Inicialização das abas Bootstrap (garante que funcionem corretamente)
    const dielectricTabsEl = document.getElementById('dielectricTabs');
    if (dielectricTabsEl) {
        const dielectricTabs = new bootstrap.Tab(dielectricTabsEl.querySelector('#basic-analysis-tab'));
        dielectricTabs.show(); // Ativa a primeira aba por padrão ao carregar o script

        dielectricTabsEl.addEventListener('shown.bs.tab', event => {
            console.log(`Aba "${event.target.id}" mostrada.`);
            // Lógica que precisa ser ativada quando uma aba é mostrada
            if (event.target.id === 'comprehensive-analysis-tab') {
                // Ao mostrar a aba de análise comparativa, podemos chamar o botão de análise automática
                const analisarDetalhesBtn = document.getElementById('analisar-detalhes-button');
                if (analisarDetalhesBtn) {
                    analisarDetalhesBtn.click(); // Simula o clique no botão
                }
            }
        });
    }


    // Função auxiliar para obter valor de input
    function getInputValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : null;
    }

    // Lógica para o botão "Salvar Parâmetros" (na aba Dados de Ensaio)
    const saveParamsBtn = document.getElementById('save-dielectric-params-btn');
    if (saveParamsBtn) {
        saveParamsBtn.addEventListener('click', function() {
            console.log('Botão Salvar Parâmetros Dielétricos clicado!');
            // Implementar lógica para coletar todos os inputs da aba "Dados de Ensaio"
            // Ex: const um_at = getInputValue('um_at');
            // Enviar dados para o backend ou armazenar em um store local (localStorage/sessionStorage)
            document.getElementById('dielectric-save-confirmation').textContent = 'Parâmetros salvos!';
            setTimeout(() => {
                document.getElementById('dielectric-save-confirmation').textContent = '';
            }, 3000);
        });
    }

    // Lógica para o botão "Analisar Detalhes" (na aba Análise Comparativa)
    const analisarDetalhesBtn = document.getElementById('analisar-detalhes-button');
    if (analisarDetalhesBtn) {
        analisarDetalhesBtn.addEventListener('click', function() {
            console.log('Botão Analisar Detalhes (Análise Comparativa) clicado!');
            // Implementar lógica para carregar os "Parâmetros Selecionados"
            // e os "Resultados da Análise" (AT, BT, Terciário)
            // Isso geralmente envolveria ler os dados salvos da aba anterior (ou do backend)
            // e fazer cálculos/comparações para preencher as divs de output.
            document.getElementById('selected-params-display').innerHTML = '<div class="text-center py-3">Parâmetros carregados e análise iniciada.</div>';
            document.getElementById('comparison-output-at').innerHTML = '<div class="text-muted text-center py-3">Resultados AT em processamento...</div>';
            document.getElementById('comparison-output-bt').innerHTML = '<div class="text-muted text-center py-3">Resultados BT em processamento...</div>';
            document.getElementById('comparison-output-terciario').innerHTML = '<div class="text-muted text-center py-3">Resultados Terciário em processamento...</div>';

            // Simulação de carregamento e preenchimento
            setTimeout(() => {
                document.getElementById('selected-params-display').innerHTML = `
                    <p><strong>Tipo Isolamento:</strong> ${getInputValue('tipo-isolamento') || 'Uniforme'}</p>
                    <p><strong>Um AT:</strong> ${getInputValue('um_at') || '-'} kV</p>
                    <p><strong>BIL AT:</strong> ${getInputValue('ia_at') || '-'} kV</p>
                    <p>... (outros parâmetros selecionados)</p>
                `;
                document.getElementById('comparison-output-at').innerHTML = `
                    <p><strong>Status AT:</strong> APROVADO</p>
                    <p><strong>NBI Normativo:</strong> 650 kV</p>
                    <p><strong>BIL Compatível:</strong> Sim</p>
                `;
                document.getElementById('comparison-output-bt').innerHTML = `
                    <p><strong>Status BT:</strong> APROVADO</p>
                    <p><strong>NBI Normativo:</strong> 110 kV</p>
                    <p><strong>BIL Compatível:</strong> Sim</p>
                `;
                document.getElementById('comparison-output-terciario').innerHTML = `
                    <p><strong>Status Ter:</strong> N/A (não configurado)</p>
                `;
            }, 1500);
        });
    }

    // Lógica para o botão "Forçar Carregamento Dados" (na aba Análise Comparativa)
    const forcarCarregamentoBtn = document.getElementById('forcar-carregamento-button');
    if (forcarCarregamentoBtn) {
        forcarCarregamentoBtn.addEventListener('click', function() {
            console.log('Botão Forçar Carregamento Dados (Análise Comparativa) clicado!');
            // Implementar lógica para recarregar todos os dados do transformador e os inputs da página
            // Isso seria útil se os dados de "Dados Básicos" tivessem sido atualizados em outra aba
            // e você quisesse que eles se refletissem aqui sem ter que recalcular tudo.
        });
    }

    // Lógica para preencher "Informações Complementares"
    // Isso é um exemplo, os dados reais viriam do "transformerDataStore"
    const fillComplementaryInfo = () => {
        const tipoIsolamentoEl = document.getElementById('tipo-isolamento');
        const displayTipoTransformadorEl = document.getElementById('display-tipo-transformador-dieletric');

        const transformerData = transformerDataStore.getData();
        if (transformerData && transformerData.inputs && transformerData.inputs.dados_basicos) {
            const basicData = transformerData.inputs.dados_basicos;
            if (tipoIsolamentoEl) tipoIsolamentoEl.textContent = basicData.tipo_isolamento || '-';
            if (displayTipoTransformadorEl) displayTipoTransformadorEl.textContent = basicData.tipo_transformador || '-';
        } else {
            if (tipoIsolamentoEl) tipoIsolamentoEl.textContent = "-";
            if (displayTipoTransformadorEl) displayTipoTransformadorEl.textContent = "-";
        }
    };

    fillComplementaryInfo(); // Chama ao carregar o script

    // Lógica para alternar para a aba "Análise Comparativa" se o botão "Análise Dielétrica Completa" for clicado
    const switchToComprehensiveBtn = document.getElementById('switch-to-comprehensive-tab-btn');
    if (switchToComprehensiveBtn) {
        switchToComprehensiveBtn.addEventListener('click', function() {
            const comprehensiveTab = new bootstrap.Tab(document.getElementById('comprehensive-analysis-tab'));
            comprehensiveTab.show(); // Ativa a aba de Análise Comparativa
        });
    }

    // Lógica para alternar para a aba "Dados de Ensaio" se o botão "Voltar" (na aba Comparativa) for clicado
    const switchToBasicBtn = document.getElementById('switch-to-basic-tab-btn');
    if (switchToBasicBtn) {
        switchToBasicBtn.addEventListener('click', function() {
            const basicTab = new bootstrap.Tab(document.getElementById('basic-analysis-tab'));
            basicTab.show(); // Ativa a aba de Dados de Ensaio
        });
    }
});