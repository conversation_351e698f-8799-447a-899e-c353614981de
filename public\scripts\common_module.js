// public/scripts/common_module.js

// Função para aguardar o sistema de persistência estar disponível
async function waitForApiSystem() {
    let attempts = 0;
    const maxAttempts = 50; // 5 segundos máximo

    while (!window.apiDataSystem && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }

    return window.apiDataSystem || null;
}

// Store de dados do transformador usando o novo sistema
const transformerDataStore = {
    async getData() {
        try {
            const apiSystem = await waitForApiSystem();
            if (apiSystem) {
                const store = apiSystem.getStore('transformerInputs');
                const data = await store.getData();
                return data;
            } else {
                // Fallback para localStorage
                return JSON.parse(localStorage.getItem('transformerInputsData')) || {};
            }
        } catch (error) {
            console.error('[transformerDataStore] Erro ao obter dados:', error);
            return {};
        }
    },

    async setData(newData) {
        try {
            const apiSystem = await waitForApiSystem();
            if (apiSystem) {
                const store = apiSystem.getStore('transformerInputs');
                await store.updateData(newData);
                console.log("[transformerDataStore] Dados atualizados via apiDataSystem");
            } else {
                // Fallback para localStorage
                localStorage.setItem('transformerInputsData', JSON.stringify(newData));
                console.log("[transformerDataStore] Dados atualizados via localStorage (fallback)");
            }
        } catch (error) {
            console.error('[transformerDataStore] Erro ao salvar dados:', error);
        }
    }
};

// Funções para preencher o transformer_info_panel
async function loadAndPopulateTransformerInfo(targetElementId) {
    const targetElement = document.getElementById(targetElementId);
    if (!targetElement) {
        // Não mostra erro para transformer_inputs pois é esperado não ter o painel
        if (targetElementId.includes('transformer_inputs')) {
            console.log(`[loadAndPopulateTransformerInfo] Painel não necessário para ${targetElementId}`);
        } else {
            console.error(`Elemento alvo para info do transformador não encontrado: ${targetElementId}`);
        }
        return;
    }

    try {
        // Carrega o template HTML do painel de informações
        const response = await fetch('templates/transformer_info_panel.html'); // Caminho relativo ao index.html
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status} ao carregar template.`);
        }
        const templateHtml = await response.text();
        targetElement.innerHTML = templateHtml;

        // Busca os dados básicos do transformador via apiDataSystem
        const transformerData = await transformerDataStore.getData();

        // Extrai dados do formato correto (pode estar em formData ou no nível raiz)
        let basicData = null;
        if (transformerData) {
            // Tenta diferentes estruturas de dados
            if (transformerData.formData) {
                basicData = transformerData.formData;
            } else if (transformerData.inputs && transformerData.inputs.dados_basicos) {
                basicData = transformerData.inputs.dados_basicos;
            } else {
                basicData = transformerData;
            }
        }

        // Função auxiliar para preencher campo com verificação de existência
        const fillField = (elementId, value) => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value || '-';
            }
        };        // Preenche os campos do template com os dados
        if (basicData && Object.keys(basicData).length > 0) {
            console.log("[loadAndPopulateTransformerInfo] Preenchendo painel com dados:", basicData);

            // Especificações Gerais
            fillField('info-potencia-mva', basicData.potencia_mva);
            fillField('info-frequencia', basicData.frequencia);
            fillField('info-tipo-transformador', basicData.tipo_transformador);
            fillField('info-grupo-ligacao', basicData.grupo_ligacao);
            fillField('info-liquido-isolante', basicData.liquido_isolante);
            fillField('info-norma-iso', basicData.norma_iso);            // Temperaturas e Pesos
            fillField('info-elevacao-oleo-topo', basicData.elevacao_oleo_topo);
            fillField('info-elevacao-enrol', basicData.elevacao_enrol);
            fillField('info-peso-parte-ativa', basicData.peso_parte_ativa);
            fillField('info-peso-tanque', basicData.peso_tanque_acessorios); // Mapped to peso_tanque_acessorios
            fillField('info-peso-oleo', basicData.peso_oleo);
            fillField('info-peso-total', basicData.peso_total);
            fillField('info-tipo-isolamento', basicData.tipo_isolamento);

            // Dados da Alta Tensão (AT)
            fillField('info-tensao-at', basicData.tensao_at);
            fillField('info-classe-tensao-at', basicData.classe_tensao_at);
            fillField('info-corrente-nominal-at', basicData.corrente_nominal_at);
            fillField('info-impedancia', basicData.impedancia);
            fillField('info-nbi-at', basicData.nbi_at);
            fillField('info-conexao-at', basicData.conexao_at);

            // TAPs AT
            fillField('info-tensao-at-tap-maior', basicData.tensao_at_tap_maior);
            fillField('info-tensao-at-tap-menor', basicData.tensao_at_tap_menor);
            fillField('info-corrente-nominal-at-tap-maior', basicData.corrente_nominal_at_tap_maior);
            fillField('info-corrente-nominal-at-tap-menor', basicData.corrente_nominal_at_tap_menor);
            fillField('info-impedancia-tap-maior', basicData.impedancia_tap_maior);
            fillField('info-impedancia-tap-menor', basicData.impedancia_tap_menor);
            fillField('info-degrau-comutador', basicData.degrau_comutador);
            fillField('info-num-degraus-comutador', basicData.num_degraus_comutador);
            fillField('info-posicao-comutador', basicData.posicao_comutador);

            // Dados da Baixa Tensão (BT)
            fillField('info-tensao-bt', basicData.tensao_bt);
            fillField('info-classe-tensao-bt', basicData.classe_tensao_bt);
            fillField('info-corrente-nominal-bt', basicData.corrente_nominal_bt);
            fillField('info-nbi-bt', basicData.nbi_bt);
            fillField('info-conexao-bt', basicData.conexao_bt);

            // Dados do Terciário
            fillField('info-tensao-terciario', basicData.tensao_terciario);
            fillField('info-classe-tensao-terciario', basicData.classe_tensao_terciario);
            fillField('info-corrente-nominal-terciario', basicData.corrente_nominal_terciario);
            fillField('info-nbi-terciario', basicData.nbi_terciario);
            fillField('info-conexao-terciario', basicData.conexao_terciario);
            fillField('info-impedancia-at-terciario', basicData.impedancia_at_terciario);
            fillField('info-impedancia-bt-terciario', basicData.impedancia_bt_terciario);

            // Tensões de Ensaio
            fillField('info-teste-tensao-aplicada-at', basicData.teste_tensao_aplicada_at);
            fillField('info-teste-tensao-induzida-at', basicData.teste_tensao_induzida_at);
            fillField('info-teste-tensao-aplicada-bt', basicData.teste_tensao_aplicada_bt);
            fillField('info-teste-tensao-aplicada-terciario', basicData.teste_tensao_aplicada_terciario);

            // Ensaios e Perdas
            fillField('info-perdas-vazio', basicData.perdas_vazio);
            fillField('info-perdas-curto-circuito', basicData.perdas_curto_circuito);
            fillField('info-corrente-excitacao', basicData.corrente_excitacao);
            fillField('info-fator-k', basicData.fator_k);
            fillField('info-classe-precisao', basicData.classe_precisao);
            fillField('info-frequencia-ressonancia', basicData.frequencia_ressonancia);

        } else {
            console.log("[loadAndPopulateTransformerInfo] Nenhum dado do transformador encontrado - exibindo campos vazios");
            // Limpa os campos se não houver dados (comportamento normal para formulário vazio)
            
            // Especificações Gerais
            fillField('info-potencia-mva', '');
            fillField('info-frequencia', '');
            fillField('info-tipo-transformador', '');
            fillField('info-grupo-ligacao', '');
            fillField('info-liquido-isolante', '');
            fillField('info-norma-iso', '');            // Temperaturas e Pesos
            fillField('info-elevacao-oleo-topo', '');
            fillField('info-elevacao-enrol', '');
            fillField('info-peso-parte-ativa', '');
            fillField('info-peso-tanque', '');
            fillField('info-peso-oleo', '');
            fillField('info-peso-total', '');
            fillField('info-tipo-isolamento', '');

            // Dados da Alta Tensão
            fillField('info-tensao-at', '');
            fillField('info-classe-tensao-at', '');
            fillField('info-corrente-nominal-at', '');
            fillField('info-impedancia', '');
            fillField('info-nbi-at', '');
            fillField('info-conexao-at', '');

            // TAPs AT
            fillField('info-tensao-at-tap-maior', '');
            fillField('info-tensao-at-tap-menor', '');
            fillField('info-corrente-nominal-at-tap-maior', '');
            fillField('info-corrente-nominal-at-tap-menor', '');
            fillField('info-impedancia-tap-maior', '');
            fillField('info-impedancia-tap-menor', '');
            fillField('info-degrau-comutador', '');
            fillField('info-num-degraus-comutador', '');
            fillField('info-posicao-comutador', '');

            // Dados da Baixa Tensão
            fillField('info-tensao-bt', '');
            fillField('info-classe-tensao-bt', '');
            fillField('info-corrente-nominal-bt', '');
            fillField('info-nbi-bt', '');
            fillField('info-conexao-bt', '');

            // Dados do Terciário
            fillField('info-tensao-terciario', '');
            fillField('info-classe-tensao-terciario', '');
            fillField('info-corrente-nominal-terciario', '');
            fillField('info-nbi-terciario', '');
            fillField('info-conexao-terciario', '');
            fillField('info-impedancia-at-terciario', '');
            fillField('info-impedancia-bt-terciario', '');

            // Tensões de Ensaio
            fillField('info-teste-tensao-aplicada-at', '');
            fillField('info-teste-tensao-induzida-at', '');
            fillField('info-teste-tensao-aplicada-bt', '');
            fillField('info-teste-tensao-aplicada-terciario', '');

            // Ensaios e Perdas
            fillField('info-perdas-vazio', '');
            fillField('info-perdas-curto-circuito', '');
            fillField('info-corrente-excitacao', '');
            fillField('info-fator-k', '');
            fillField('info-classe-precisao', '');
            fillField('info-frequencia-ressonancia', '');
        }

    } catch (error) {
        console.error('Erro ao carregar ou preencher o painel de informações do transformador:', error);
        targetElement.innerHTML = `
            <div class="alert alert-danger m-0 p-2" style="font-size: 0.75rem;">
                Erro ao carregar informações do transformador.
            </div>
        `;
    }
}

// Função para atualizar o painel de informações globalmente
async function updateGlobalInfoPanel() {
    // Esta função pode ser chamada quando dados do transformador são atualizados
    // Procura por painéis de informação em todas as páginas e os atualiza
    const infoPanels = document.querySelectorAll('.transformer-info-panel');
    if (infoPanels.length > 0) {
        // Se encontrar painéis, recarrega as informações
        const targetElementId = infoPanels[0].closest('[id]')?.id;
        if (targetElementId) {
            await loadAndPopulateTransformerInfo(targetElementId);
        }
    }
}

// Configura listener para atualização automática quando dados do transformador mudam
function setupAutoUpdateListener() {
    document.addEventListener('transformerDataUpdated', async (event) => {
        console.log('[common_module] Dados do transformador atualizados, atualizando painéis:', event.detail);
        await updateGlobalInfoPanel();
    });
    console.log('[common_module] Listener de atualização automática configurado');
}

// Limpeza automática de cache para módulos específicos
function autoCleanModuleCache(moduleName) {
    const moduleVersion = '1.0.0';
    const versionKey = `${moduleName}_cache_version`;
    const lastVersion = localStorage.getItem(versionKey);

    if (lastVersion !== moduleVersion) {
        console.log(`[autoCleanModuleCache] Limpando cache do módulo ${moduleName}...`);

        const keys = Object.keys(localStorage);
        let removedCount = 0;

        keys.forEach(key => {
            if (key.includes(moduleName)) {
                localStorage.removeItem(key);
                removedCount++;
            }
        });

        localStorage.setItem(versionKey, moduleVersion);
        console.log(`[autoCleanModuleCache] ✅ ${moduleName}: ${removedCount} itens removidos`);
    }
}

// Inicializa o listener quando o DOM estiver pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupAutoUpdateListener);
} else {
    setupAutoUpdateListener();
}

// Limpa cache automaticamente para módulos problemáticos
autoCleanModuleCache('history');
autoCleanModuleCache('standards');

// Exporta as funções para serem usadas pelos scripts de módulo
export { loadAndPopulateTransformerInfo, transformerDataStore, updateGlobalInfoPanel };