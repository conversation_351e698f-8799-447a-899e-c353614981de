// public/scripts/common_module.js

// Função para simular um store de dados do transformador (seria via API no backend)
// Por enquanto, usaremos localStorage para simular persistência simples
const transformerDataStore = {
    _data: JSON.parse(localStorage.getItem('transformerInputsData')) || {},

    getData: function() {
        return this._data;
    },
    setData: function(newData) {
        this._data = newData;
        localStorage.setItem('transformerInputsData', JSON.stringify(newData));
        console.log("Dados do transformador atualizados no store/localStorage.");
    }
};

// Funções para preencher o transformer_info_panel
async function loadAndPopulateTransformerInfo(targetElementId) {
    const targetElement = document.getElementById(targetElementId);
    if (!targetElement) {
        console.error(`Elemento alvo para info do transformador não encontrado: ${targetElementId}`);
        return;
    }

    try {
        // Carrega o template HTML do painel de informações
        const response = await fetch('templates/transformer_info_panel.html'); // Caminho relativo ao index.html
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status} ao carregar template.`);
        }
        const templateHtml = await response.text();
        targetElement.innerHTML = templateHtml;

        // Tenta buscar os dados básicos do transformador (simulando um store/API)
        // No futuro, isso viria de um dcc.Store global ou de uma API
        const transformerData = transformerDataStore.getData(); // Obtém os dados do "store"

        // Preenche os campos do template com os dados
        if (transformerData && transformerData.inputs && transformerData.inputs.dados_basicos) {
            const basicData = transformerData.inputs.dados_basicos;

            document.getElementById('info-potencia-mva').textContent = basicData.potencia_mva || '-';
            document.getElementById('info-frequencia').textContent = basicData.frequencia || '-';
            document.getElementById('info-tipo-transformador').textContent = basicData.tipo_transformador || '-';
            document.getElementById('info-grupo-ligacao').textContent = basicData.grupo_ligacao || '-';

            // Dados da Alta Tensão
            document.getElementById('info-tensao-at').textContent = basicData.tensao_at || '-';
            document.getElementById('info-corrente-nominal-at').textContent = basicData.corrente_nominal_at || '-';
            document.getElementById('info-impedancia').textContent = basicData.impedancia || '-'; // Impedância é um campo AT
            document.getElementById('info-nbi-at').textContent = basicData.nbi_at || '-';


            // Dados da Baixa Tensão
            document.getElementById('info-tensao-bt').textContent = basicData.tensao_bt || '-';
            document.getElementById('info-corrente-nominal-bt').textContent = basicData.corrente_nominal_bt || '-';

            // Adicione mais campos conforme necessário
        } else {
            console.warn("Dados básicos do transformador não encontrados no store para preencher o painel de informações.");
            // Limpa os campos se não houver dados
            document.getElementById('info-potencia-mva').textContent = '-';
            document.getElementById('info-frequencia').textContent = '-';
            document.getElementById('info-tipo-transformador').textContent = '-';
            document.getElementById('info-grupo-ligacao').textContent = '-';
            document.getElementById('info-tensao-at').textContent = '-';
            document.getElementById('info-corrente-nominal-at').textContent = '-';
            document.getElementById('info-impedancia').textContent = '-';
            document.getElementById('info-nbi-at').textContent = '-';
            document.getElementById('info-tensao-bt').textContent = '-';
            document.getElementById('info-corrente-nominal-bt').textContent = '-';
        }

    } catch (error) {
        console.error('Erro ao carregar ou preencher o painel de informações do transformador:', error);
        targetElement.innerHTML = `
            <div class="alert alert-danger m-0 p-2" style="font-size: 0.75rem;">
                Erro ao carregar informações do transformador.
            </div>
        `;
    }
}

// Exporta as funções para serem usadas pelos scripts de módulo
export { loadAndPopulateTransformerInfo, transformerDataStore };