// public/scripts/common_module.js

// Função para aguardar o sistema de persistência estar disponível
async function waitForApiSystem() {
    let attempts = 0;
    const maxAttempts = 50; // 5 segundos máximo

    while (!window.apiDataSystem && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }

    return window.apiDataSystem || null;
}

// Store de dados do transformador usando o novo sistema
const transformerDataStore = {
    async getData() {
        try {
            const apiSystem = await waitForApiSystem();
            if (apiSystem) {
                const store = apiSystem.getStore('transformerInputs');
                const data = await store.getData();
                return data;
            } else {
                // Fallback para localStorage
                return JSON.parse(localStorage.getItem('transformerInputsData')) || {};
            }
        } catch (error) {
            console.error('[transformerDataStore] Erro ao obter dados:', error);
            return {};
        }
    },

    async setData(newData) {
        try {
            const apiSystem = await waitForApiSystem();
            if (apiSystem) {
                const store = apiSystem.getStore('transformerInputs');
                await store.updateData(newData);
                console.log("[transformerDataStore] Dados atualizados via apiDataSystem");
            } else {
                // Fallback para localStorage
                localStorage.setItem('transformerInputsData', JSON.stringify(newData));
                console.log("[transformerDataStore] Dados atualizados via localStorage (fallback)");
            }
        } catch (error) {
            console.error('[transformerDataStore] Erro ao salvar dados:', error);
        }
    }
};

// Funções para preencher o transformer_info_panel
async function loadAndPopulateTransformerInfo(targetElementId) {
    const targetElement = document.getElementById(targetElementId);
    if (!targetElement) {
        console.error(`Elemento alvo para info do transformador não encontrado: ${targetElementId}`);
        return;
    }

    try {
        // Carrega o template HTML do painel de informações
        const response = await fetch('templates/transformer_info_panel.html'); // Caminho relativo ao index.html
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status} ao carregar template.`);
        }
        const templateHtml = await response.text();
        targetElement.innerHTML = templateHtml;

        // Busca os dados básicos do transformador via apiDataSystem
        const transformerData = await transformerDataStore.getData();

        // Extrai dados do formato correto (pode estar em formData ou no nível raiz)
        let basicData = null;
        if (transformerData) {
            // Tenta diferentes estruturas de dados
            if (transformerData.formData) {
                basicData = transformerData.formData;
            } else if (transformerData.inputs && transformerData.inputs.dados_basicos) {
                basicData = transformerData.inputs.dados_basicos;
            } else {
                basicData = transformerData;
            }
        }

        // Função auxiliar para preencher campo com verificação de existência
        const fillField = (elementId, value) => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value || '-';
            }
        };

        // Preenche os campos do template com os dados
        if (basicData && Object.keys(basicData).length > 0) {
            console.log("[loadAndPopulateTransformerInfo] Preenchendo painel com dados:", basicData);

            fillField('info-potencia-mva', basicData.potencia_mva);
            fillField('info-frequencia', basicData.frequencia);
            fillField('info-tipo-transformador', basicData.tipo_transformador);
            fillField('info-grupo-ligacao', basicData.grupo_ligacao);

            // Dados da Alta Tensão
            fillField('info-tensao-at', basicData.tensao_at);
            fillField('info-corrente-nominal-at', basicData.corrente_nominal_at);
            fillField('info-impedancia', basicData.impedancia);
            fillField('info-nbi-at', basicData.nbi_at);

            // Dados da Baixa Tensão
            fillField('info-tensao-bt', basicData.tensao_bt);
            fillField('info-corrente-nominal-bt', basicData.corrente_nominal_bt);

            // Adicione mais campos conforme necessário
        } else {
            console.log("[loadAndPopulateTransformerInfo] Nenhum dado do transformador encontrado - exibindo campos vazios");
            // Limpa os campos se não houver dados (comportamento normal para formulário vazio)
            fillField('info-potencia-mva', '');
            fillField('info-frequencia', '');
            fillField('info-tipo-transformador', '');
            fillField('info-grupo-ligacao', '');
            fillField('info-tensao-at', '');
            fillField('info-corrente-nominal-at', '');
            fillField('info-impedancia', '');
            fillField('info-nbi-at', '');
            fillField('info-tensao-bt', '');
            fillField('info-corrente-nominal-bt', '');
        }

    } catch (error) {
        console.error('Erro ao carregar ou preencher o painel de informações do transformador:', error);
        targetElement.innerHTML = `
            <div class="alert alert-danger m-0 p-2" style="font-size: 0.75rem;">
                Erro ao carregar informações do transformador.
            </div>
        `;
    }
}

// Função para atualizar o painel de informações globalmente
function updateGlobalInfoPanel() {
    // Esta função pode ser chamada quando dados do transformador são atualizados
    // Procura por painéis de informação em todas as páginas e os atualiza
    const infoPanels = document.querySelectorAll('.transformer-info-panel');
    if (infoPanels.length > 0) {
        // Se encontrar painéis, recarrega as informações
        const targetElementId = infoPanels[0].closest('[id]')?.id;
        if (targetElementId) {
            loadAndPopulateTransformerInfo(targetElementId);
        }
    }
}

// Exporta as funções para serem usadas pelos scripts de módulo
export { loadAndPopulateTransformerInfo, transformerDataStore, updateGlobalInfoPanel };