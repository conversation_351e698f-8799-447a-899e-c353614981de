"""
Serviço para cálculos de perdas em transformadores
"""
import sys
import pathlib
import math
from pydantic import BaseModel
from typing import Dict, List, Optional, Union, Any

# Ajusta o path para permitir importações corretas
current_file = pathlib.Path(__file__).absolute()
current_dir = current_file.parent
backend_dir = current_dir.parent
root_dir = backend_dir.parent

# Adiciona os diretórios ao path se ainda não estiverem lá
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))


class NoLoadLossesInput(BaseModel):
    """Modelo para os dados de entrada de perdas em vazio"""
    perdas_vazio_kw: float
    peso_nucleo_ton: float
    corrente_excitacao: float
    inducao_nucleo: float
    corrente_excitacao_1_1: Optional[float] = None
    corrente_excitacao_1_2: Optional[float] = None
    potencia_mva: float
    frequencia: float
    tensao_at: float


class LoadLossesInput(BaseModel):
    """Modelo para os dados de entrada de perdas em carga"""
    temperatura_referencia: int
    perdas_carga_kw_u_min: float
    perdas_carga_kw_u_nom: float
    perdas_carga_kw_u_max: float
    potencia_mva: float
    impedancia: float


def calculate_no_load_losses(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcula os resultados de perdas em vazio com base nos parâmetros de entrada.
    
    Args:
        input_data: Dicionário com os parâmetros de entrada
    
    Returns:
        Dicionário com os resultados dos cálculos
    """
    # Converter dicionário para modelo de dados
    data = NoLoadLossesInput(**input_data)
    
    # Cálculos de perdas específicas e outros resultados
    perdas_especificas = data.perdas_vazio_kw / data.peso_nucleo_ton  # kW/ton
    
    # Calcular densidade de potência (VA/kg)
    densidade_potencia = data.potencia_mva * 1000 / (data.peso_nucleo_ton * 1000)
    
    # Calcular fator de qualidade do núcleo (aproximado)
    fator_qualidade = 0.95 - (0.05 * data.inducao_nucleo / 1.7) if data.inducao_nucleo > 0 else 0.95
    
    # Calcular perdas por unidade
    perdas_por_unidade = data.perdas_vazio_kw / (data.potencia_mva * 10)  # % em base de 10 kW/MVA
    
    # Resultados para os níveis de tensão
    resultados_dut = {
        "perdas_vazio_100": data.perdas_vazio_kw,
        "corrente_excitacao_100": data.corrente_excitacao,
        "perdas_vazio_110": data.perdas_vazio_kw * 1.21 if data.inducao_nucleo > 0 else None,  # Estimativa P ~ V²
        "corrente_excitacao_110": data.corrente_excitacao_1_1 or (data.corrente_excitacao * 2 if data.inducao_nucleo > 0 else None),
        "perdas_vazio_120": data.perdas_vazio_kw * 1.44 if data.inducao_nucleo > 0 else None,  # Estimativa P ~ V²
        "corrente_excitacao_120": data.corrente_excitacao_1_2 or (data.corrente_excitacao * 4 if data.inducao_nucleo > 0 else None),
    }
    
    # Parâmetros gerais e de material
    parametros_gerais = {
        "perdas_especificas": round(perdas_especificas, 2),  # W/kg
        "densidade_potencia": round(densidade_potencia, 2),  # VA/kg
        "fator_qualidade": round(fator_qualidade, 3),
        "inducao_nucleo": data.inducao_nucleo,
        "perdas_por_unidade": round(perdas_por_unidade, 2),  # %
    }
    
    return {
        "parametros_gerais": parametros_gerais,
        "resultados_dut": resultados_dut,
        "analise_taps": {
            "message": "Análise de taps disponível apenas para cálculos completos."
        }
    }


def calculate_load_losses(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcula os resultados de perdas em carga com base nos parâmetros de entrada.
    
    Args:
        input_data: Dicionário com os parâmetros de entrada
    
    Returns:
        Dicionário com os resultados dos cálculos
    """
    # Converter dicionário para modelo de dados
    data = LoadLossesInput(**input_data)
    
    # Realizar cálculos básicos
    temperatura_ref = data.temperatura_referencia
    
    # Correção de temperatura para cálculo de perdas
    fator_correcao = (235 + 75) / (235 + temperatura_ref)  # Para referência a 75°C
    
    # Perdas corrigidas
    perdas_corrigidas_min = data.perdas_carga_kw_u_min * fator_correcao
    perdas_corrigidas_nom = data.perdas_carga_kw_u_nom * fator_correcao
    perdas_corrigidas_max = data.perdas_carga_kw_u_max * fator_correcao
    
    # Perdas por unidade (base de 10 kW/MVA)
    perdas_pu_nominal = perdas_corrigidas_nom / (data.potencia_mva * 10)
    
    # Perdas I²R e adicionais (estimativas)
    i2r_perdas = perdas_corrigidas_nom * 0.85  # ~85% são perdas I²R
    perdas_adicionais = perdas_corrigidas_nom - i2r_perdas
    
    return {
        "condicoes_nominais": {
            "temperatura_referencia": temperatura_ref,
            "perdas_tap_menos": round(perdas_corrigidas_min, 2),
            "perdas_tap_nominal": round(perdas_corrigidas_nom, 2),
            "perdas_tap_mais": round(perdas_corrigidas_max, 2),
            "perdas_por_unidade": round(perdas_pu_nominal * 100, 2),  # Em percentual
            "i2r_perdas": round(i2r_perdas, 2),
            "perdas_adicionais": round(perdas_adicionais, 2),
            "impedancia": data.impedancia
        }
    }


def calculate_cap_bank(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcula o banco de capacitores requerido para compensação de potência reativa.
    
    Args:
        input_data: Dicionário com os parâmetros de entrada
    
    Returns:
        Dicionário com os resultados do cálculo do banco de capacitores
    """
    # Extrair potência de referência e fator de potência desejado
    potencia_ativa = input_data.get("potencia_ativa", 0)  # kW
    fp_atual = input_data.get("fator_potencia_atual", 0.7)
    fp_desejado = input_data.get("fator_potencia_desejado", 0.95)
    
    # Evitar divisão por zero e outros valores inválidos
    if potencia_ativa <= 0 or fp_atual <= 0 or fp_atual >= 1 or fp_desejado <= 0 or fp_desejado >= 1:
        return {
            "potencia_reativa_atual": 0,
            "potencia_reativa_desejada": 0,
            "potencia_banco_capacitores": 0,
            "status": "Valores de entrada inválidos."
        }
    
    # Cálculo de potências reativas
    tg_phi_atual = math.sqrt(1 - fp_atual**2) / fp_atual
    tg_phi_desejado = math.sqrt(1 - fp_desejado**2) / fp_desejado
    
    potencia_reativa_atual = potencia_ativa * tg_phi_atual  # kVAR
    potencia_reativa_desejada = potencia_ativa * tg_phi_desejado  # kVAR
    
    # Potência do banco de capacitores
    potencia_banco_capacitores = potencia_reativa_atual - potencia_reativa_desejada  # kVAR
    
    return {
        "potencia_reativa_atual": round(potencia_reativa_atual, 2),
        "potencia_reativa_desejada": round(potencia_reativa_desejada, 2),
        "potencia_banco_capacitores": round(potencia_banco_capacitores, 2),
        "status": "OK"
    }


def calculate_sut_eps_current_compensated(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcula a corrente compensada para o sistema SUT/EPS com banco de capacitores.
    
    Args:
        input_data: Dicionário com os parâmetros de entrada
    
    Returns:
        Dicionário com os resultados da análise de corrente compensada
    """
    # Extrair dados da análise básica
    potencia_ativa = input_data.get("potencia_ativa", 0)  # kW
    tensao_teste = input_data.get("tensao_teste", 0)  # kV
    fator_potencia = input_data.get("fator_potencia", 0.7)
    potencia_banco_cap = input_data.get("potencia_banco_cap", 0)  # kVAR
    
    # Constantes do sistema
    sut_bt_voltage = 480  # V (SUT_BT_VOLTAGE)
    eps_current_limit = 2000  # A (EPS_CURRENT_LIMIT)
    
    # Evitar divisão por zero
    if tensao_teste <= 0:
        return {
            "corrente_sem_compensacao": 0,
            "corrente_compensada": 0,
            "percentual_limite": 0,
            "status": "Tensão de teste inválida."
        }
    
    # Cálculo da potência aparente sem compensação
    potencia_reativa = potencia_ativa * math.sqrt(1 - fator_potencia**2) / fator_potencia  # kVAR
    potencia_aparente = math.sqrt(potencia_ativa**2 + potencia_reativa**2)  # kVA
    
    # Cálculo da potência reativa compensada
    potencia_reativa_compensada = potencia_reativa - potencia_banco_cap  # kVAR
    potencia_aparente_compensada = math.sqrt(potencia_ativa**2 + potencia_reativa_compensada**2)  # kVA
    
    # Fator de potência compensado
    fp_compensado = potencia_ativa / potencia_aparente_compensada if potencia_aparente_compensada > 0 else 1.0
    
    # Cálculos de corrente no lado primário (BT) do SUT
    ratio_sut = tensao_teste * 1000 / sut_bt_voltage
    
    corrente_sem_compensacao = potencia_aparente * 1000 / (sut_bt_voltage * math.sqrt(3)) * ratio_sut
    corrente_compensada = potencia_aparente_compensada * 1000 / (sut_bt_voltage * math.sqrt(3)) * ratio_sut
    
    percentual_limite = corrente_compensada / eps_current_limit * 100 if eps_current_limit > 0 else 100
    
    return {
        "corrente_sem_compensacao": round(corrente_sem_compensacao, 2),
        "corrente_compensada": round(corrente_compensada, 2),
        "fator_potencia_compensado": round(fp_compensado, 3),
        "percentual_limite": round(percentual_limite, 2),
        "status": "OK" if percentual_limite < 100 else "Acima do limite"
    }


def analyze_losses(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Realiza análise completa de perdas em vazio e em carga.
    
    Args:
        data: Dicionário com todos os parâmetros necessários
        
    Returns:
        Dicionário com os resultados completos da análise de perdas
    """
    # Prepara dados para análise de perdas em vazio
    no_load_data = {
        "perdas_vazio_kw": data.get("perdas_vazio", 0),
        "peso_nucleo_ton": data.get("peso_nucleo", 0),
        "corrente_excitacao": data.get("corrente_excitacao", 0),
        "inducao_nucleo": data.get("inducao_nucleo", 1.7),
        "corrente_excitacao_1_1": data.get("corrente_excitacao_1_1", None),
        "corrente_excitacao_1_2": data.get("corrente_excitacao_1_2", None),
        "potencia_mva": data.get("potencia_mva", 0),
        "frequencia": data.get("frequencia", 60),
        "tensao_at": data.get("tensao_at", 0)
    }
    
    # Prepara dados para análise de perdas em carga
    load_data = {
        "temperatura_referencia": data.get("temperatura_referencia", 75),
        "perdas_carga_kw_u_min": data.get("perdas_carga_kw_u_min", 0),
        "perdas_carga_kw_u_nom": data.get("perdas_carga_kw_u_nom", 0),
        "perdas_carga_kw_u_max": data.get("perdas_carga_kw_u_max", 0),
        "potencia_mva": data.get("potencia_mva", 0),
        "impedancia": data.get("impedancia", 0)
    }
    
    # Prepara dados para cálculo do banco de capacitores
    cap_bank_data = {
        "potencia_ativa": load_data["perdas_carga_kw_u_nom"],
        "fator_potencia_atual": 0.7,  # Valor típico sem compensação
        "fator_potencia_desejado": 0.95  # Objetivo típico após compensação
    }
    
    # Executa os cálculos
    no_load_results = calculate_no_load_losses(no_load_data)
    load_results = calculate_load_losses(load_data)
    cap_bank_results = calculate_cap_bank(cap_bank_data)
    
    # Prepara dados para análise SUT/EPS com compensação
    sut_eps_data = {
        "potencia_ativa": load_data["perdas_carga_kw_u_nom"],
        "tensao_teste": data.get("tensao_at", 0),
        "fator_potencia": 0.7,  # Valor típico sem compensação
        "potencia_banco_cap": cap_bank_results["potencia_banco_capacitores"]
    }
    
    # Calcula corrente compensada
    sut_eps_results = calculate_sut_eps_current_compensated(sut_eps_data)
    
    # Combina todos os resultados
    results = {
        "perdas_vazio": no_load_results,
        "perdas_carga": load_results,
        "banco_capacitores": cap_bank_results,
        "analise_sut_eps": sut_eps_results
    }
    
    return results