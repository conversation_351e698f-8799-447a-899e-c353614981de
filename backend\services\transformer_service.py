# backend/services/transformer_service.py

import logging
import sys
import os
import pathlib
from typing import Dict, Any, Optional

# Ajusta o path para permitir importações corretas
current_file = pathlib.Path(__file__).absolute()
current_dir = current_file.parent
backend_dir = current_dir.parent
root_dir = backend_dir.parent

# Adiciona os diretórios ao path se ainda não estiverem lá
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Tenta várias estratégias de importação
try:
    # Tenta importação relativa (quando importado como submódulo)
    from ..utils import constants as const
except ImportError:
    try:
        # Tenta importação absoluta (quando executado diretamente)
        from backend.utils import constants as const
    except ImportError:
        try:
            # Tenta importação local (quando executado de backend/)
            from utils import constants as const
        except ImportError:
            logging.warning("Não foi possível importar 'constants'. Usando valores mock para constantes.")
            
            # Define constantes mock que podem ser necessárias
            class MockConstants:
                IAC_NBI_FACTOR = 3.5  # Valor padrão para o fator IAC NBI
                EPSILON = 1e-6        # Tolerância para comparações
                
            const = MockConstants()
            
            # Mock para calculate_nominal_currents se não puder ser importado
            def calculate_nominal_currents(data: Dict[str, Any]) -> Dict[str, Optional[float]]:
                logging.warning("Usando mock para calculate_nominal_currents.")
                potencia_mva = data.get("potencia_mva")
                tensao_at = data.get("tensao_at")
                tensao_bt = data.get("tensao_bt")
                tensao_terciario = data.get("tensao_terciario")
                tipo_transformador = data.get("tipo_transformador", "Trifásico")
                
                sqrt_3 = 1.732 if tipo_transformador == "Trifásico" else 1.0
                
                corrente_at = (potencia_mva * 1000) / (tensao_at * sqrt_3) if potencia_mva and tensao_at and tensao_at > 0 else None
                corrente_bt = (potencia_mva * 1000) / (tensao_bt * sqrt_3) if potencia_mva and tensao_bt and tensao_bt > 0 else None
                corrente_terciario = (potencia_mva * 1000) / (tensao_terciario * sqrt_3) if potencia_mva and tensao_terciario and tensao_terciario > 0 else None

                return {
                    "corrente_nominal_at": corrente_at,
                    "corrente_nominal_bt": corrente_bt,
                    "corrente_nominal_terciario": corrente_terciario,
                    "corrente_nominal_at_tap_maior": None, # Mock
                    "corrente_nominal_bt_tap_maior": None  # Mock
                }

log = logging.getLogger(__name__)

def safe_float_convert(value: Any) -> Optional[float]:
    """Converte um valor para float, retornando None se a conversão falhar ou o valor for vazio."""
    if value is None or (isinstance(value, str) and value.strip() == ""):
        return None
    try:
        if isinstance(value, str):
            value = value.replace(",", ".").strip()
        return float(value)
    except (ValueError, TypeError):
        return None

def extract_and_process_transformer_inputs(raw_inputs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extrai e processa os dados brutos de entrada do transformador,
    normalizando tipos e tratando valores vazios.
    """
    processed_data = {}

    # Dados Básicos
    processed_data["potencia_mva"] = safe_float_convert(raw_inputs.get("potencia_mva"))
    processed_data["frequencia"] = safe_float_convert(raw_inputs.get("frequencia"))
    processed_data["tipo_transformador"] = raw_inputs.get("tipo_transformador", "Trifásico")
    processed_data["grupo_ligacao"] = raw_inputs.get("grupo_ligacao")
    processed_data["liquido_isolante"] = raw_inputs.get("liquido_isolante")
    processed_data["tipo_isolamento"] = raw_inputs.get("tipo_isolamento")
    processed_data["norma_iso"] = raw_inputs.get("norma_iso")
    processed_data["elevacao_oleo_topo"] = safe_float_convert(raw_inputs.get("elevacao_oleo_topo"))
    processed_data["elevacao_enrol"] = safe_float_convert(raw_inputs.get("elevacao_enrol"))

    # Pesos
    processed_data["peso_total"] = safe_float_convert(raw_inputs.get("peso_total"))
    processed_data["peso_parte_ativa"] = safe_float_convert(raw_inputs.get("peso_parte_ativa"))
    processed_data["peso_oleo"] = safe_float_convert(raw_inputs.get("peso_oleo"))
    processed_data["peso_tanque_acessorios"] = safe_float_convert(raw_inputs.get("peso_tanque_acessorios"))

    # Alta Tensão
    processed_data["tensao_at"] = safe_float_convert(raw_inputs.get("tensao_at"))
    processed_data["classe_tensao_at"] = safe_float_convert(raw_inputs.get("classe_tensao_at"))
    processed_data["impedancia"] = safe_float_convert(raw_inputs.get("impedancia"))
    processed_data["nbi_at"] = safe_float_convert(raw_inputs.get("nbi_at"))
    processed_data["sil_at"] = safe_float_convert(raw_inputs.get("sil_at"))
    processed_data["conexao_at"] = raw_inputs.get("conexao_at")
    processed_data["tensao_bucha_neutro_at"] = safe_float_convert(raw_inputs.get("tensao_bucha_neutro_at"))
    processed_data["nbi_neutro_at"] = safe_float_convert(raw_inputs.get("nbi_neutro_at"))
    processed_data["sil_neutro_at"] = safe_float_convert(raw_inputs.get("sil_neutro_at"))
    processed_data["teste_tensao_aplicada_at"] = safe_float_convert(raw_inputs.get("teste_tensao_aplicada_at"))
    processed_data["teste_tensao_induzida_at"] = safe_float_convert(raw_inputs.get("teste_tensao_induzida_at"))

    # Taps AT
    processed_data["tensao_at_tap_maior"] = safe_float_convert(raw_inputs.get("tensao_at_tap_maior"))
    processed_data["impedancia_tap_maior"] = safe_float_convert(raw_inputs.get("impedancia_tap_maior"))
    processed_data["tensao_at_tap_menor"] = safe_float_convert(raw_inputs.get("tensao_at_tap_menor"))
    processed_data["impedancia_tap_menor"] = safe_float_convert(raw_inputs.get("impedancia_tap_menor"))

    # Baixa Tensão
    processed_data["tensao_bt"] = safe_float_convert(raw_inputs.get("tensao_bt"))
    processed_data["classe_tensao_bt"] = safe_float_convert(raw_inputs.get("classe_tensao_bt"))
    processed_data["nbi_bt"] = safe_float_convert(raw_inputs.get("nbi_bt"))
    processed_data["sil_bt"] = safe_float_convert(raw_inputs.get("sil_bt"))
    processed_data["conexao_bt"] = raw_inputs.get("conexao_bt")
    processed_data["tensao_bucha_neutro_bt"] = safe_float_convert(raw_inputs.get("tensao_bucha_neutro_bt"))
    processed_data["nbi_neutro_bt"] = safe_float_convert(raw_inputs.get("nbi_neutro_bt"))
    processed_data["sil_neutro_bt"] = safe_float_convert(raw_inputs.get("sil_neutro_bt"))
    processed_data["teste_tensao_aplicada_bt"] = safe_float_convert(raw_inputs.get("teste_tensao_aplicada_bt"))

    # Terciário
    processed_data["tensao_terciario"] = safe_float_convert(raw_inputs.get("tensao_terciario"))
    processed_data["classe_tensao_terciario"] = safe_float_convert(raw_inputs.get("classe_tensao_terciario"))
    processed_data["nbi_terciario"] = safe_float_convert(raw_inputs.get("nbi_terciario"))
    processed_data["sil_terciario"] = safe_float_convert(raw_inputs.get("sil_terciario"))
    processed_data["conexao_terciario"] = raw_inputs.get("conexao_terciario")
    processed_data["tensao_bucha_neutro_terciario"] = safe_float_convert(raw_inputs.get("tensao_bucha_neutro_terciario"))
    processed_data["nbi_neutro_terciario"] = safe_float_convert(raw_inputs.get("nbi_neutro_terciario"))
    processed_data["sil_neutro_terciario"] = safe_float_convert(raw_inputs.get("sil_neutro_terciario"))
    processed_data["teste_tensao_aplicada_terciario"] = safe_float_convert(raw_inputs.get("teste_tensao_aplicada_terciario"))

    return processed_data

def calculate_and_process_transformer_data(
    transformer_inputs: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Realiza cálculos de correntes nominais e adiciona dados derivados
    aos inputs do transformador.

    Args:
        transformer_inputs (Dict[str, Any]): Dicionário com os inputs brutos do transformador.

    Returns:
        Dict[str, Any]: Dados do transformador processados com correntes e IAC calculados.
    """
    log.info("Iniciando cálculo e processamento de dados do transformador.")

    # 1. Processar e limpar os inputs brutos
    data_to_process = extract_and_process_transformer_inputs(transformer_inputs)

    # 2. Calcular correntes nominais
    currents_input_data = {
        "tipo_transformador": data_to_process.get("tipo_transformador"),
        "potencia_mva": data_to_process.get("potencia_mva"),
        "tensao_at": data_to_process.get("tensao_at"),
        "tensao_at_tap_maior": data_to_process.get("tensao_at_tap_maior"),
        "tensao_at_tap_menor": data_to_process.get("tensao_at_tap_menor"),
        "tensao_bt": data_to_process.get("tensao_bt"),
        "tensao_terciario": data_to_process.get("tensao_terciario"),
    }
    # Usar a função calculate_nominal_currents definida neste arquivo
    calculated_currents = calculate_nominal_currents(currents_input_data)

    data_to_process["corrente_nominal_at"] = calculated_currents.get("corrente_nominal_at")
    data_to_process["corrente_nominal_bt"] = calculated_currents.get("corrente_nominal_bt")
    data_to_process["corrente_nominal_terciario"] = calculated_currents.get("corrente_nominal_terciario")
    data_to_process["corrente_nominal_at_tap_maior"] = calculated_currents.get("corrente_nominal_at_tap_maior")
    data_to_process["corrente_nominal_at_tap_menor"] = calculated_currents.get("corrente_nominal_at_tap_menor")

    # 3. Calcular IAC (Impulso Atmosférico Cortado) se a norma for IEC
    norma_para_iac = data_to_process.get("norma_iso")
    if norma_para_iac and "IEC" in norma_para_iac:
        for winding_prefix in ["at", "bt", "terciario"]:
            nbi_key = f"nbi_{winding_prefix}"
            nbi_value = data_to_process.get(nbi_key)
            
            iac_value = None
            if nbi_value is not None:
                try:
                    iac_value = round(const.IAC_NBI_FACTOR * float(nbi_value), 2)
                except (ValueError, TypeError):
                    log.warning(f"Não foi possível calcular IAC para {winding_prefix} a partir de {nbi_key}: {nbi_value}")
            data_to_process[f"iac_{winding_prefix}"] = iac_value
    else:
        data_to_process["iac_at"] = None
        data_to_process["iac_bt"] = None
        data_to_process["iac_terciario"] = None

    # 4. Sincronizar elevação de enrolamento
    if data_to_process.get("elevacao_enrol") is not None:
        elevacao = data_to_process["elevacao_enrol"]
        data_to_process["elevacao_enrol_at"] = elevacao
        data_to_process["elevacao_enrol_bt"] = elevacao
        data_to_process["elevacao_enrol_terciario"] = elevacao

    log.info("Cálculo e processamento de dados do transformador concluídos.")
    return data_to_process