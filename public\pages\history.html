<!-- public/pages/history.html -->
<div class="container-fluid ps-2 pe-0 d-flex flex-column" style="min-height: calc(100vh - 120px);">
    <!-- Div onde o painel de informações do transformador será injetado -->
    <div id="transformer-info-history-page" class="mb-2"></div>
    <!-- Divs ocultas para compatibilidade global --><!-- <PERSON><PERSON><PERSON><PERSON> Página -->
    <div class="row g-2 pt-2">
        <div class="col-md-12">
            <h4 class="d-inline align-middle">
                <i class="fas fa-history me-2" style="color: var(--accent-color);"></i>
                Histórico de Sessões
            </h4>
        </div>
    </div>

    <!-- Seção de Gerenciamento de Sessões (Salvar e Buscar) -->
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="m-0">
                <i class="fas fa-tasks me-2"></i> Gerenciar Sessões
            </h6>
        </div>
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-7 col-lg-8 mb-2 mb-md-0">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="history-search-input" placeholder="Filtrar por nome ou notas...">
                        <button type="button" class="btn btn-primary ms-2" id="history-search-button">Buscar</button>
                    </div>
                </div>
                <div class="col-md-5 col-lg-4">
                    <button type="button" class="btn btn-success w-100" id="history-open-save-modal-button">
                        <i class="fas fa-save me-2"></i> Salvar Sessão Atual
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="card" style="background-color: var(--background-card-header); margin-bottom: 1rem; padding: 0.5rem;">
        <div class="card-body p-0">
            <div class="row g-0">
                <div class="col-md-4" style="background-color: var(--background-card-header); padding: 0.75rem; text-align: center; border-right: 1px solid var(--border-color);">
                    <i class="fas fa-database" style="font-size: 1.8rem; margin-bottom: 0.5rem; color: var(--accent-color);"></i>
                    <div>
                        <h5 id="history-stats-total-sessions" style="font-size: 1.4rem; font-weight: bold; margin: 0; color: var(--text-light);">--</h5>
                        <p style="font-size: 0.75rem; margin: 0; color: var(--text-muted);">Sessões Salvas</p>
                    </div>
                </div>
                <!-- Adicione mais estatísticas se desejar aqui -->
            </div>
        </div>
    </div>

    <!-- Tabela de Sessões Armazenadas -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0" style="font-size: 1rem; font-weight: bold; color: var(--text-header);">
                <i class="fas fa-list-alt me-2"></i> Sessões Armazenadas
            </h6>
        </div>
        <div class="card-body">
            <div id="history-loading-table">
                <div class="row g-0" style="position: sticky; top: 0; z-index: 1; background-color: var(--background-card-header);">
                    <div class="col-3" style="background-color: var(--background-card-header); color: var(--text-header); padding: 8px 12px; font-weight: 600; text-align: center; font-size: 0.8rem; border: 1px solid var(--border-color); border-bottom-width: 2px;">Data/Hora</div>
                    <div class="col-4" style="background-color: var(--background-card-header); color: var(--text-header); padding: 8px 12px; font-weight: 600; text-align: center; font-size: 0.8rem; border: 1px solid var(--border-color); border-bottom-width: 2px;">Nome da Sessão</div>
                    <div class="col-3" style="background-color: var(--background-card-header); color: var(--text-header); padding: 8px 12px; font-weight: 600; text-align: center; font-size: 0.8rem; border: 1px solid var(--border-color); border-bottom-width: 2px;">Notas</div>
                    <div class="col-2" style="background-color: var(--background-card-header); color: var(--text-header); padding: 8px 12px; font-weight: 600; text-align: center; font-size: 0.8rem; border: 1px solid var(--border-color); border-bottom-width: 2px;">Ações</div>
                </div>
                <div id="history-table-body-content" style="max-height: 60vh; overflow-y: auto; border: 1px solid var(--border-color); border-top: none;">
                    <!-- Conteúdo da tabela preenchido por JavaScript -->
                    <div class="text-muted text-center py-5">Nenhuma sessão encontrada.</div>
                </div>
            </div>
            <div id="history-action-message" class="mt-3" style="min-height: 40px;"></div>
        </div>
    </div>

    <!-- Modal para Salvar Sessão -->
    <div class="modal fade" id="history-save-session-modal" tabindex="-1" aria-labelledby="historySaveSessionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background-color: var(--background-card); color: var(--text-light);">
                <div class="modal-header" style="background-color: var(--background-card-header); color: var(--text-header);">
                    <h5 class="modal-title" id="historySaveSessionModalLabel">Salvar Sessão Atual</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <label for="history-session-name-input" class="form-label">Nome da Sessão:</label>
                    <input type="text" class="form-control mb-3" id="history-session-name-input" placeholder="Identificador único">
                    <label for="history-session-notes-input" class="form-label">Notas (Opcional):</label>
                    <textarea class="form-control" id="history-session-notes-input" placeholder="Detalhes..." style="height: 100px;"></textarea>
                    <div id="history-save-modal-error" class="text-danger small mt-2"></div>
                </div>
                <div class="modal-footer" style="background-color: var(--background-card-header); border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="history-save-modal-cancel-button">Cancelar</button>
                    <button type="button" class="btn btn-success" id="history-save-modal-confirm-button">
                        <i class="fas fa-save me-1"></i> Salvar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Confirmar Exclusão -->
    <div class="modal fade" id="history-delete-session-modal" tabindex="-1" aria-labelledby="historyDeleteSessionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background-color: var(--background-card); color: var(--text-light);">
                <div class="modal-header" style="background-color: var(--background-card-header); color: var(--text-header);">
                    <h5 class="modal-title" id="historyDeleteSessionModalLabel">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir esta sessão?</p>
                    <p class="fw-bold" style="color: var(--danger-color);">Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer" style="background-color: var(--background-card-header); border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="history-delete-modal-cancel-button">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="history-delete-modal-confirm-button">
                        <i class="fas fa-trash-alt me-1"></i> Excluir
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>