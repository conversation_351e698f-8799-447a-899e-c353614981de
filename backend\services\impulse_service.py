"""
Serviço para cálculos de ensaios de impulso
Implementa os algoritmos descritos em docs/instrucoes_impulso.md
"""

import sys
import pathlib
from typing import Dict, Any, Optional, Union, List, Tuple
import math
import logging
import numpy as np
from scipy.optimize import fsolve

# Ajusta o path para permitir importações corretas
current_file = pathlib.Path(__file__).absolute()
current_dir = current_file.parent
backend_dir = current_dir.parent
root_dir = backend_dir.parent

# Adiciona os diretórios ao path se ainda não estiverem lá
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Tenta importar constantes para o serviço
try:
    from ..utils import constants as const
except ImportError:
    try:
        from backend.utils import constants as const
    except ImportError:
        try:
            from utils import constants as const
        except ImportError:
            logging.warning("Não foi possível importar 'constants'. Usando mock para constantes.")
            class MockConstants:
                EPSILON = 1e-6
                # Constantes para impulso
                LIGHTNING_IMPULSE_FRONT_TIME_NOM = 1.2  # μs
                LIGHTNING_IMPULSE_TAIL_TIME_NOM = 50.0  # μs
                LIGHTNING_FRONT_TOLERANCE = 0.30  # 30%
                LIGHTNING_TAIL_TOLERANCE = 0.20   # 20%
                
            const = MockConstants()


def calculate_impulse_waveform_parameters(resistor_frontal: float, resistor_cauda: float, 
                                         capacitancia_gerador: float, capacitancia_objeto: float) -> Dict[str, Any]:
    """
    Calcula os parâmetros da forma de onda de impulso conforme seção 3.1 da documentação.
    
    Args:
        resistor_frontal: Valor do resistor frontal em Ohms
        resistor_cauda: Valor do resistor de cauda em Ohms
        capacitancia_gerador: Capacitância do gerador em nF
        capacitancia_objeto: Capacitância do objeto de teste em pF
        
    Returns:
        Dicionário com os parâmetros calculados (alfa, beta, tempo de frente, tempo de cauda, eficiência)
    """
    # Converte capacitância do objeto para nF para cálculos consistentes
    c_objeto_nf = capacitancia_objeto / 1000.0
    
    # Cálculo de alfa e beta conforme seção 3.1
    alfa = 1 / (resistor_cauda * c_objeto_nf)
    beta = 1 / (resistor_frontal * capacitancia_gerador)
    
    # Cálculo dos tempos de frente e cauda
    tempo_frente, tempo_cauda = calculate_front_tail_times(alfa, beta)
    
    # Cálculo da eficiência
    eficiencia = calculate_efficiency(alfa, beta)
    
    return {
        "alfa": alfa,
        "beta": beta,
        "tempo_frente": tempo_frente,
        "tempo_cauda": tempo_cauda,        "eficiencia": eficiencia,
        "dentro_tolerancia_frente": is_within_tolerance(tempo_frente, 
                                                      const.LIGHTNING_IMPULSE_FRONT_TIME_NOM, 
                                                      const.LIGHTNING_FRONT_TOLERANCE),
        "dentro_tolerancia_cauda": is_within_tolerance(tempo_cauda,
                                                     const.LIGHTNING_IMPULSE_TAIL_TIME_NOM,
                                                     const.LIGHTNING_TAIL_TOLERANCE)
    }


def impulse_waveform(t: float, alfa: float, beta: float) -> float:
    """
    Calcula o valor da tensão na forma de onda de impulso para um dado tempo.
    
    Args:
        t: Tempo em μs
        alfa: Parâmetro alfa da equação de impulso
        beta: Parâmetro beta da equação de impulso
        
    Returns:
        Valor relativo da tensão (0-1) para o tempo t
    """
    return np.exp(-alfa * t) - np.exp(-beta * t)


def calculate_front_tail_times(alfa: float, beta: float) -> Tuple[float, float]:
    """
    Calcula os tempos de frente e cauda da forma de onda de impulso.
    
    Args:
        alfa: Parâmetro alfa da equação de impulso
        beta: Parâmetro beta da equação de impulso
        
    Returns:
        Tupla (tempo_frente, tempo_cauda) em μs
    """
    # Tempo para o pico (quando a derivada é zero)
    # d/dt [e^(-αt) - e^(-βt)] = 0
    # -α*e^(-αt) + β*e^(-βt) = 0
    # β*e^(-βt) = α*e^(-αt)
    # ln(β) - βt = ln(α) - αt
    # ln(β) - ln(α) = βt - αt
    # ln(β/α) = (β-α)t
    # t = ln(β/α)/(β-α)
    t_pico = np.log(beta / alfa) / (beta - alfa)
    
    # Valor máximo da forma de onda
    v_max = impulse_waveform(t_pico, alfa, beta)
    
    # Funções para encontrar os tempos onde a forma de onda atinge 30% e 90% do pico na subida
    def func_30pct(t):
        return impulse_waveform(t, alfa, beta) - 0.3 * v_max
    
    def func_90pct(t):
        return impulse_waveform(t, alfa, beta) - 0.9 * v_max
        
    # Função para encontrar o tempo onde a forma de onda atinge 50% do pico na descida
    def func_50pct_descida(t):
        return impulse_waveform(t, alfa, beta) - 0.5 * v_max
    
    # Estima pontos iniciais para resolver as equações
    t0_30pct = 0.1  # μs
    t0_90pct = 0.9  # μs
    t0_50pct = 40.0  # μs
    
    # Resolve para encontrar os tempos precisos
    t_30pct = fsolve(func_30pct, t0_30pct)[0]
    t_90pct = fsolve(func_90pct, t0_90pct)[0]
    
    # Calcula o tempo de frente considerando a correção T₁ = 1.67 * (t₉₀ - t₃₀)
    tempo_frente = 1.67 * (t_90pct - t_30pct)
    
    # Calcula o tempo de cauda
    t_50pct_descida = fsolve(func_50pct_descida, t0_50pct)[0]
    tempo_cauda = t_50pct_descida
    
    return tempo_frente, tempo_cauda


def calculate_efficiency(alfa: float, beta: float) -> float:
    """
    Calcula a eficiência do gerador de impulso.
    
    Args:
        alfa: Parâmetro alfa da equação de impulso
        beta: Parâmetro beta da equação de impulso
        
    Returns:
        Eficiência do gerador (0-1)
    """
    # Tempo para o pico
    t_pico = np.log(beta / alfa) / (beta - alfa)
    
    # Valor máximo da forma de onda (eficiência)
    eficiencia = impulse_waveform(t_pico, alfa, beta)
    
    return eficiencia


def is_within_tolerance(valor: float, nominal: float, tolerancia: float) -> bool:
    """
    Verifica se um valor está dentro da tolerância especificada.
    
    Args:
        valor: Valor medido
        nominal: Valor nominal
        tolerancia: Tolerância em fração (ex: 0.3 para 30%)
        
    Returns:
        True se estiver dentro da tolerância, False caso contrário
    """
    limite_inferior = nominal * (1 - tolerancia)
    limite_superior = nominal * (1 + tolerancia)
    return limite_inferior <= valor <= limite_superior


def calculate_impulse_test(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcula os parâmetros do teste de impulso com base nos dados do transformador.
    
    Args:
        data: Dicionário com os parâmetros de entrada
        
    Returns:
        Dicionário com os resultados calculados para o teste de impulso
    """
    # Extrai parâmetros básicos
    tipo_transformador = data.get("tipo_transformador", "Trifásico")
    tensao_at = data.get("tensao_at", 0)
    classe_tensao_at = data.get("classe_tensao_at", 0)
    bil = data.get("nbi_at", 0)  # Nível Básico de Isolamento para AT
    
    # Parâmetros do circuito de impulso
    resistor_frontal = data.get("resistor_frontal", 500)  # Ohms
    resistor_cauda = data.get("resistor_cauda", 2000)  # Ohms
    capacitancia_gerador = data.get("capacitancia_gerador", 1.0)  # nF
    capacitancia_objeto = data.get("capacitancia_objeto", 1000.0)  # pF
    indutancia = data.get("indutancia", 5.0)  # μH
    tempo_corte = data.get("tempo_corte", 3.0)  # μs para impulso cortado
    
    # Cálculo dos parâmetros da forma de onda
    waveform_params = calculate_impulse_waveform_parameters(
        resistor_frontal, resistor_cauda, capacitancia_gerador, capacitancia_objeto
    )
    
    # Cálculo de tensões máximas
    tensao_carregamento = bil / waveform_params["eficiencia"]
    
    # Prepara resultados
    results = {
        # Parâmetros de entrada relevantes
        "bil": bil,
        "resistor_frontal": resistor_frontal,
        "resistor_cauda": resistor_cauda,
        "capacitancia_gerador": capacitancia_gerador,
        "capacitancia_objeto": capacitancia_objeto,
        "indutancia": indutancia,
        "tempo_corte": tempo_corte,
        
        # Parâmetros calculados da forma de onda
        "alfa": waveform_params["alfa"],
        "beta": waveform_params["beta"],
        "tempo_frente": waveform_params["tempo_frente"],
        "tempo_cauda": waveform_params["tempo_cauda"],
        "eficiencia": waveform_params["eficiencia"],
        "dentro_tolerancia_frente": waveform_params["dentro_tolerancia_frente"],
        "dentro_tolerancia_cauda": waveform_params["dentro_tolerancia_cauda"],
        
        # Tensões calculadas
        "tensao_carregamento": tensao_carregamento,
        "tensao_pico": bil,
    }
    
    return results