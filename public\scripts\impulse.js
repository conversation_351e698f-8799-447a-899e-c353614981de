// public/scripts/impulse.js - ATUALIZADO

import { loadAndPopulateTransformerInfo } from './common_module.js';
import { setupApiFormPersistence } from './api_persistence.js';

// Função de inicialização do módulo Impulso
async function initImpulse() {
    console.log('Módulo Impulso carregado e pronto para interatividade.');

    // ID do placeholder para o painel de informações do transformador
    const transformerInfoPlaceholderId = 'transformer-info-impulse-page';
    await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId); // Carrega o painel no topo
    
    // Configurar persistência automática do formulário
    const impulseStore = window.apiDataSystem.getStore('impulse');
    // Configurar persistência de dados usando API do backend
    await setupApiFormPersistence('impulse-form', 'impulse');

    // Configurar spinners para campos numéricos
    setupNumericSpinners();
    
    // Configurar botão de simulação
    setupSimulationButton();
}

// Função para configurar spinners dos campos numéricos
function setupNumericSpinners() {
    const spinnerConfigs = [
        { upId: 'voltage-up', downId: 'voltage-down', inputId: 'test-voltage', step: 10 },
        { upId: 'dut-cap-up', downId: 'dut-cap-down', inputId: 'test-object-capacitance', step: 100 },
        { upId: 'stray-cap-up', downId: 'stray-cap-down', inputId: 'stray-capacitance', step: 50 }
    ];

    spinnerConfigs.forEach(config => {
        const upBtn = document.getElementById(config.upId);
        const downBtn = document.getElementById(config.downId);
        const input = document.getElementById(config.inputId);

        if (upBtn && input) {
            upBtn.addEventListener('click', () => {
                const currentValue = parseFloat(input.value) || 0;
                input.value = currentValue + config.step;
                // Dispara evento 'input' para detecção imediata de mudanças
                input.dispatchEvent(new Event('input', { bubbles: true }));
                // Dispara evento 'change' para acionar persistência
                input.dispatchEvent(new Event('change', { bubbles: true }));
                // Fornece feedback visual
                upBtn.classList.add('active');
                setTimeout(() => upBtn.classList.remove('active'), 200);
            });
        }

        if (downBtn && input) {
            downBtn.addEventListener('click', () => {
                const currentValue = parseFloat(input.value) || 0;
                const newValue = Math.max(0, currentValue - config.step);
                input.value = newValue;
                // Dispara evento 'input' para detecção imediata de mudanças
                input.dispatchEvent(new Event('input', { bubbles: true }));
                // Dispara evento 'change' para acionar persistência
                input.dispatchEvent(new Event('change', { bubbles: true }));
                // Fornece feedback visual
                downBtn.classList.add('active');
                setTimeout(() => downBtn.classList.remove('active'), 200);
            });
        }
    });
}

// Função para configurar o botão de simulação
function setupSimulationButton() {
    const simulateButton = document.getElementById('simulate-button');
    if (simulateButton) {
        simulateButton.addEventListener('click', async function() {
            console.log('Botão Simular Impulso clicado!');
            
            // Desabilita o botão durante a simulação
            simulateButton.disabled = true;
            simulateButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Simulando...';
            
            try {
                // Coleta dados do formulário
                const formData = await getImpulseFormData();
                
                // Simula o processamento (substitua por chamada real à API)
                await simulateImpulseTest(formData);
                
            } catch (error) {
                console.error('Erro na simulação:', error);
            } finally {
                // Reabilita o botão
                simulateButton.disabled = false;
                simulateButton.innerHTML = '<i class="fas fa-play"></i> Simular';
            }
        });
    }
}

// Função para coletar dados do formulário de impulso
async function getImpulseFormData() {
    const store = window.apiDataSystem.getStore('impulse');
    const impulseData = await store.getData();
    
    // Coleta dados específicos dos elementos do formulário
    const formData = {
        impulseType: document.querySelector('input[name="impulseType"]:checked')?.value || 'lightning',
        testVoltage: document.getElementById('test-voltage')?.value || 1200,
        generatorConfig: document.getElementById('generator-config')?.value || '6S-1P',
        simulationModel: document.getElementById('simulation-model-type')?.value || 'hybrid',
        dutCapacitance: document.getElementById('test-object-capacitance')?.value || 3000,
        shuntResistor: document.getElementById('shunt-resistor')?.value || 0.01,
        strayCapacitance: document.getElementById('stray-capacitance')?.value || 400,
        ...impulseData.formData
    };
    
    return formData;
}

// Função para simular o teste de impulso
async function simulateImpulseTest(formData) {
    console.log('Simulando teste de impulso com dados:', formData);
    
    // Simula delay de processamento
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Gera resultados simulados
    const results = {
        waveform: generateSimulatedWaveform(formData),
        parameters: {
            frontTime: 1.2, // µs
            tailTime: 50,   // µs
            peakVoltage: formData.testVoltage,
            efficiency: 0.85
        },
        timestamp: new Date().toISOString()
    };
    
    // Salva os resultados
    await saveCalculationResults(results);
    
    // Atualiza a interface com os resultados
    displaySimulationResults(results);
    
    console.log('Simulação concluída:', results);
}

// Função para gerar forma de onda simulada
function generateSimulatedWaveform(formData) {
    // Gera pontos de uma forma de onda 1.2/50µs simplificada
    const points = [];
    const totalTime = 100; // µs
    const dt = 0.1; // µs
    
    for (let t = 0; t <= totalTime; t += dt) {
        let voltage = 0;
        
        if (t <= 1.2) {
            // Frente de onda
            voltage = formData.testVoltage * (t / 1.2);
        } else {
            // Cauda de onda
            voltage = formData.testVoltage * Math.exp(-(t - 1.2) / 50);
        }
        
        points.push({ time: t, voltage: voltage });
    }
    
    return points;
}

// Função para exibir resultados da simulação
function displaySimulationResults(results) {
    // Atualiza elementos da interface com os resultados
    const resultArea = document.getElementById('simulation-results');
    if (resultArea) {
        resultArea.innerHTML = `
            <div class="alert alert-success">
                <h6>Simulação Concluída</h6>
                <p><strong>Tensão de Pico:</strong> ${results.parameters.peakVoltage} kV</p>
                <p><strong>Tempo de Frente:</strong> ${results.parameters.frontTime} µs</p>
                <p><strong>Tempo de Cauda:</strong> ${results.parameters.tailTime} µs</p>
                <p><strong>Eficiência:</strong> ${(results.parameters.efficiency * 100).toFixed(1)}%</p>
            </div>
        `;
    }
}

async function getTransformerFormData() {
    const store = window.apiDataSystem.getStore('transformerInputs');
    const transformerData = await store.getData();
    return transformerData.formData || {};
}

async function saveCalculationResults(results) {
    const store = window.apiDataSystem.getStore('impulse');
    await store.updateData({ calculationResults: results });
}

// SPA routing: executa quando o módulo impulse é carregado
document.addEventListener('moduleContentLoaded', (event) => {
    if (event.detail && event.detail.moduleName === 'impulse') {
        console.log('[impulse] SPA routing init');
        initImpulse();
    }
});

// Fallback para carregamento direto da página (se não for SPA)
document.addEventListener('DOMContentLoaded', () => {
    // Verifica se o elemento principal do módulo está presente para evitar execução em outras páginas
    if (document.getElementById('impulse-form')) { // Assumindo um ID de formulário principal para o módulo de impulso
        console.log('[impulse] DOMContentLoaded init (fallback)');
        initImpulse();
    }
});