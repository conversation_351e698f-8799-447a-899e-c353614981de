// public/scripts/main.js - Versão Atualizada para Roteamento SPA

document.addEventListener('DOMContentLoaded', () => {
    // --- Existing Functionality (Preserved and Integrated) ---
    // Theme Toggle
    const themeToggle = document.getElementById('theme-toggle');
    const themeToggleIcon = document.getElementById('theme-toggle-icon');
    const themeToggleText = document.getElementById('theme-toggle-text');
    const body = document.body;
    
    // Function to apply the selected theme
    function applyTheme(theme) {
        body.setAttribute('data-bs-theme', theme);
        if (theme === 'dark') {
            if (themeToggleIcon) themeToggleIcon.classList.replace('fa-sun', 'fa-moon'); 
            if (themeToggleText) themeToggleText.textContent = 'Tema Escuro'; 
        } else {
            if (themeToggleIcon) themeToggleIcon.classList.replace('fa-moon', 'fa-sun'); 
            if (themeToggleText) themeToggleText.textContent = 'Tema Claro'; 
        }
        localStorage.setItem('theme', theme);
    }

    // Initialize theme based on localStorage or default to dark
    const storedTheme = localStorage.getItem('theme');
    applyTheme(storedTheme || 'dark');

    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            let newTheme = body.getAttribute('data-bs-theme') === 'dark' ? 'light' : 'dark';
            applyTheme(newTheme);
        });
    }

    // Modal for Clearing Fields
    const globalClearButton = document.getElementById('global-clear-button');
    const clearConfirmModalElement = document.getElementById('clearConfirmModal');
    let clearConfirmModalInstance;
    if (clearConfirmModalElement) {
        clearConfirmModalInstance = new bootstrap.Modal(clearConfirmModalElement);
    }
    const clearConfirmButton = document.getElementById('clear-confirm-button');

    if (globalClearButton && clearConfirmModalInstance) {
        globalClearButton.addEventListener('click', () => {
            clearConfirmModalInstance.show();
        });
    }

    if (clearConfirmButton && clearConfirmModalInstance) {
        clearConfirmButton.addEventListener('click', () => {
            const scopeElement = document.querySelector('input[name="clearScopeRadio"]:checked');
            if (scopeElement) {
                const scope = scopeElement.value;
                console.log(`Confirmado: Limpar ${scope}`);
                
                if (scope === 'current_module') {
                    const currentModuleHash = window.location.hash.substring(1);
                    const moduleToClear = currentModuleHash || 'transformer_inputs';
                    const event = new CustomEvent('clearModuleData', { detail: { module: moduleToClear } });
                    document.dispatchEvent(event);
                    console.log(`Event clearModuleData dispatched for module: ${moduleToClear}`);
                } else if (scope === 'all_modules') {
                    const event = new CustomEvent('clearAllModulesData');
                    document.dispatchEvent(event);
                    console.log("Event clearAllModulesData dispatched.");
                }
            }
            clearConfirmModalInstance.hide();
        });
    }
    
    // Usage Counter Simulation
    const usageValueElement = document.getElementById('usage-value');
    const usageBarElement = document.getElementById('usage-bar');
    const limitAlertDiv = document.getElementById('limit-alert-div');
    const MAX_USAGE = 1000; // Example limit
    let currentUsage = 0; // This should ideally be loaded/managed from a persistent state or backend

    function updateUsageDisplay() {
        if (usageValueElement) usageValueElement.textContent = currentUsage;
        if (usageBarElement) {
            const percentage = (currentUsage / MAX_USAGE) * 100;
            usageBarElement.style.width = `${Math.min(percentage, 100)}%`;
        }
        if (limitAlertDiv) {
            if (currentUsage >= MAX_USAGE) {
                limitAlertDiv.classList.remove('d-none');
            } else {
                limitAlertDiv.classList.add('d-none');
            }
        }
    }
    updateUsageDisplay();


    // --- SPA Routing Logic ---
    const mainContentArea = document.getElementById('main-content-area');
    // CORREÇÃO: O seletor agora aponta para o ID do div que contém os links de navegação.
    const navLinks = document.querySelectorAll('#navbarContent .nav-link[data-module]'); 
    
    const homeLink = document.getElementById('home-link');
    let currentModuleScriptTag = null;
    let currentLoadedModule = null;
    const moduleCache = new Map();

    function setActiveNavLink(moduleName) {
        navLinks.forEach(link => {
            if (link.dataset.module === moduleName) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    async function loadModulePage(moduleName, pushToHistory = true) {
        if (!moduleName) {
            console.warn('loadModulePage chamada sem moduleName. Usando transformer_inputs como padrão.');
            moduleName = 'transformer_inputs';
        }

        // Evita recarregar o mesmo módulo
        if (currentLoadedModule === moduleName) {
            console.log(`[main.js] Módulo ${moduleName} já está carregado. Ignorando.`);
            return;
        }

        const htmlFilePath = `pages/${moduleName}.html`; // Verifique se este caminho está correto para seus arquivos HTML
        // CORREÇÃO: Usando caminho relativo para importação dinâmica de módulos ES6
        const scriptFilePath = `./${moduleName}.js`; // Caminho relativo para módulos ES6

        // Mostra um indicador de carregamento
        if (mainContentArea) {
            mainContentArea.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="ms-3 mb-0">Carregando módulo ${moduleName}...</p>
                </div>`;
        }

        try {
            const response = await fetch(htmlFilePath);
            if (!response.ok) {
                throw new Error(`Erro HTTP ${response.status} ao buscar ${htmlFilePath}`);
            }
            const htmlContent = await response.text();
            if (mainContentArea) mainContentArea.innerHTML = htmlContent;

            if (currentModuleScriptTag) {
                currentModuleScriptTag = null;
            }

            try {
                // Verifica cache primeiro
                let module;
                if (moduleCache.has(moduleName)) {
                    module = moduleCache.get(moduleName);
                    console.log(`[main.js] Módulo ${moduleName}.js carregado do cache.`, module);
                } else {
                    module = await import(scriptFilePath);
                    moduleCache.set(moduleName, module);
                    console.log(`[main.js] Módulo ${moduleName}.js carregado dinamicamente.`, module);
                }

                currentLoadedModule = moduleName;
                document.dispatchEvent(new CustomEvent('moduleContentLoaded', { detail: { moduleName } }));

            } catch (scriptLoadError) {
                if (scriptLoadError instanceof TypeError && scriptLoadError.message.includes('Failed to fetch dynamically imported module')) {
                    console.log(`[main.js] Nenhum script de módulo encontrado ou erro de carregamento para ${moduleName} em ${scriptFilePath}. Isso pode ser normal para módulos sem JS.`, scriptLoadError);
                } else {
                    console.error(`[main.js] Erro ao carregar o script do módulo ${moduleName} em ${scriptFilePath}:`, scriptLoadError);
                }
            }
            
            setActiveNavLink(moduleName);

            if (pushToHistory) {
                history.pushState({ module: moduleName }, `${moduleName.replace('_', ' ')} - Simulador`, `#${moduleName}`);
            } else {
                document.title = `${moduleName.replace('_', ' ')} - Simulador`;
            }
            
        } catch (error) {
            console.error(`Erro ao carregar o módulo ${moduleName}:`, error);
            if (mainContentArea) {
                mainContentArea.innerHTML = `<div class="alert alert-danger m-3" style="background-color: var(--background-card); color: var(--text-light); border-left: 4px solid var(--danger-color);">
                                                <i class="fas fa-exclamation-circle me-2"></i> Erro ao carregar o módulo: ${moduleName}. Verifique o console.
                                            </div>`;
            }
            setActiveNavLink(null);
        }
    }

    navLinks.forEach(link => {
        link.addEventListener('click', (event) => {
            event.preventDefault();
            const moduleName = link.dataset.module;
            if (moduleName) {
                const currentHashModule = window.location.hash.substring(1);
                if (moduleName !== currentHashModule) {
                    loadModulePage(moduleName);
                } else {
                    console.log("Módulo já ativo:", moduleName);
                }
            }
            // Para fechar o menu hamburger em mobile após clicar
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('#navbarContent');
            if (navbarToggler && navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse, { toggle: false });
                bsCollapse.hide();
            }
        });
    });
    
    if (homeLink) {
        homeLink.addEventListener('click', (event) => {
            event.preventDefault();
            loadModulePage('transformer_inputs', true);
            // Também fechar o menu hamburger em mobile
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('#navbarContent');
            if (navbarToggler && navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse, { toggle: false });
                bsCollapse.hide();
            }
        });
    }

    window.addEventListener('popstate', (event) => {
        let moduleToLoad = 'transformer_inputs';
        if (event.state && event.state.module) {
            moduleToLoad = event.state.module;
        } else {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const isValidModule = Array.from(navLinks).some(navLink => navLink.dataset.module === hash);
                if (isValidModule) {
                    moduleToLoad = hash;
                } else {
                    console.warn(`Módulo inválido no hash em popstate: ${hash}. Carregando padrão.`);
                }
            }
        }
        loadModulePage(moduleToLoad, false);
    });

    function initializeAppRouting() {
        const initialHash = window.location.hash.substring(1);
        let moduleToLoadOnStart = 'transformer_inputs';

        if (initialHash) {
            const isValidModule = Array.from(navLinks).some(navLink => navLink.dataset.module === initialHash);
            if (isValidModule) {
                moduleToLoadOnStart = initialHash;
                loadModulePage(moduleToLoadOnStart, false);
            } else {
                console.warn(`Módulo inválido no hash da URL inicial: '${initialHash}'. Carregando módulo padrão.`);
                loadModulePage(moduleToLoadOnStart, true);
            }
        } else {
            loadModulePage(moduleToLoadOnStart, true);
        }
    }

    initializeAppRouting();
});