"""
Serviço para cálculos de elevação de temperatura
Implementa os algoritmos descritos em docs/instrucoes_temperatura.md
"""

import sys
import pathlib
from typing import Dict, Any, Optional, Union, List
import math
import logging
import numpy as np

# Ajusta o path para permitir importações corretas
current_file = pathlib.Path(__file__).absolute()
current_dir = current_file.parent
backend_dir = current_dir.parent
root_dir = backend_dir.parent

# Adiciona os diretórios ao path se ainda não estiverem lá
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Tenta importar constantes para o serviço
try:
    from ..utils import constants as const
except ImportError:
    try:
        from backend.utils import constants as const
    except ImportError:
        try:
            from utils import constants as const
        except ImportError:
            logging.warning("Não foi possível importar 'constants'. Usando mock para constantes.")
            class MockConstants:
                EPSILON = 1e-6
                # Constantes para resfriamento
                FATOR_POTENCIA_OLEO = 0.9  # Expoente para modelos de elevação de temperatura
                CALOR_ESPECIFICO_OLEO = 1.880  # kJ/(kg·K)
                CALOR_ESPECIFICO_COBRE = 0.385  # kJ/(kg·K)
                CALOR_ESPECIFICO_FERRO = 0.450  # kJ/(kg·K)
                TEMP_AMBIENTE_REFERENCIA = 20  # °C
                
            const = MockConstants()


def calculate_oil_temperature_rise(data: Dict[str, Any]) -> Dict[str, float]:
    """
    Calcula a elevação de temperatura do óleo conforme seção 3.1 da documentação.
    
    Args:
        data: Dicionário com os parâmetros do transformador
        
    Returns:
        Dicionário com os resultados de elevação de temperatura do óleo
    """
    # Extrai parâmetros relevantes
    perdas_vazio = data.get("perdas_vazio_kw", 0)
    perdas_carga = data.get("perdas_carga_kw_u_nom", 0)
    tipo_resfriamento = data.get("tipo_resfriamento", "ONAN")
    peso_oleo = data.get("peso_oleo", 0)
    carga_percentual = data.get("carga_percentual", 100) / 100
    
    # Determina o expoente n baseado no tipo de resfriamento
    if tipo_resfriamento in ["ONAN", "ONAF"]:
        n = 0.8
    elif tipo_resfriamento in ["OFAF", "OFWF"]:
        n = 0.9
    else:  # ODAF, ODWF e outros
        n = 1.0
    
    # Calcula as perdas totais em condição de carga
    perdas_totais_nominal = perdas_vazio + perdas_carga
    perdas_carga_atual = perdas_carga * (carga_percentual ** 2)
    perdas_totais_atual = perdas_vazio + perdas_carga_atual
    
    # Relação entre perdas em carga e em vazio
    r = perdas_carga / perdas_vazio if perdas_vazio > 0 else 1
    
    # Relação de perdas para condição atual de carga
    r_atual = r * (carga_percentual ** 2)
    
    # Elevação de temperatura do óleo em regime permanente (topo)
    elevacao_nominal = data.get("elevacao_oleo_topo", 55)  # K (valor típico para ONAN)
    elevacao_atual = elevacao_nominal * ((1 + r_atual) / (1 + r)) ** n
    
    # Constante de tempo do óleo (minutos)
    capacidade_termica_oleo = peso_oleo * const.CALOR_ESPECIFICO_OLEO  # kJ/K
    coef_transferencia_oleo = perdas_totais_nominal / elevacao_nominal  # kW/K
    constante_tempo_oleo = capacidade_termica_oleo / (60 * coef_transferencia_oleo)  # minutos
    
    return {
        "elevacao_oleo_nominal": elevacao_nominal,
        "elevacao_oleo_atual": elevacao_atual,
        "constante_tempo_oleo": constante_tempo_oleo,
        "n_expoente": n,
        "r_nominal": r,
        "r_atual": r_atual
    }


def calculate_winding_temperature_rise(data: Dict[str, Any]) -> Dict[str, float]:
    """
    Calcula a elevação de temperatura dos enrolamentos conforme seção 3.2 da documentação.
    
    Args:
        data: Dicionário com os parâmetros do transformador
        
    Returns:
        Dicionário com os resultados de elevação de temperatura dos enrolamentos
    """
    # Extrai parâmetros relevantes
    perdas_carga = data.get("perdas_carga_kw_u_nom", 0)
    tipo_resfriamento = data.get("tipo_resfriamento", "ONAN")
    peso_enrolamentos = data.get("peso_enrolamentos", 0)
    carga_percentual = data.get("carga_percentual", 100) / 100
    
    # Determina o expoente m baseado no tipo de resfriamento
    if tipo_resfriamento in ["ONAN"]:
        m = 0.8
    elif tipo_resfriamento in ["ONAF"]:
        m = 0.9
    else:  # OFAF, OFWF, ODAF, ODWF e outros
        m = 1.0
    
    # Calcula as perdas nos enrolamentos em condição de carga
    perdas_enrol_nominal = perdas_carga
    perdas_enrol_atual = perdas_enrol_nominal * (carga_percentual ** 2)
    
    # Gradiente de temperatura entre enrolamento e óleo
    g_nominal = data.get("elevacao_enrol", 65) - data.get("elevacao_oleo_topo", 55)  # K
    g_atual = g_nominal * (carga_percentual ** (2 * m))
    
    # Elevação de temperatura do óleo (do cálculo anterior)
    elevacao_oleo = calculate_oil_temperature_rise(data)
    elevacao_oleo_atual = elevacao_oleo["elevacao_oleo_atual"]
    
    # Elevação de temperatura dos enrolamentos em relação à ambiente
    elevacao_enrol_atual = elevacao_oleo_atual + g_atual
    
    # Constante de tempo dos enrolamentos (minutos)
    capacidade_termica_enrol = peso_enrolamentos * const.CALOR_ESPECIFICO_COBRE  # kJ/K
    coef_transferencia_enrol = perdas_enrol_nominal / g_nominal  # kW/K
    constante_tempo_enrol = capacidade_termica_enrol / (60 * coef_transferencia_enrol)  # minutos
    
    return {
        "gradiente_nominal": g_nominal,
        "gradiente_atual": g_atual,
        "elevacao_enrol_atual": elevacao_enrol_atual,
        "constante_tempo_enrol": constante_tempo_enrol,
        "m_expoente": m
    }


def calculate_temperature_time_curve(data: Dict[str, Any], tempo_total: float = 480, intervalo: float = 10) -> Dict[str, Any]:
    """
    Calcula a curva de temperatura ao longo do tempo conforme seção 2.2 da documentação.
    
    Args:
        data: Dicionário com os parâmetros do transformador
        tempo_total: Tempo total da simulação em minutos
        intervalo: Intervalo entre pontos em minutos
        
    Returns:
        Dicionário com os arrays de tempo e temperaturas calculadas
    """
    # Extrai parâmetros relevantes
    temp_ambiente = data.get("temp_ambiente", const.TEMP_AMBIENTE_REFERENCIA)
    carga_inicial_pct = data.get("carga_inicial_pct", 0) / 100
    carga_final_pct = data.get("carga_percentual", 100) / 100
    
    # Cria dados temporários para os cálculos iniciais
    data_inicial = data.copy()
    data_inicial["carga_percentual"] = carga_inicial_pct * 100
    
    # Calcula elevações de temperatura para estado inicial e final
    oleo_inicial = calculate_oil_temperature_rise(data_inicial)
    enrol_inicial = calculate_winding_temperature_rise(data_inicial)
    
    data_final = data.copy()
    data_final["carga_percentual"] = carga_final_pct * 100
    
    oleo_final = calculate_oil_temperature_rise(data_final)
    enrol_final = calculate_winding_temperature_rise(data_final)
    
    # Constantes de tempo
    tau_oleo = oleo_final["constante_tempo_oleo"]
    tau_enrol = enrol_final["constante_tempo_enrol"]
    
    # Valores iniciais e finais
    theta_oleo_inicial = oleo_inicial["elevacao_oleo_atual"]
    theta_oleo_final = oleo_final["elevacao_oleo_atual"]
    
    theta_enrol_inicial = enrol_inicial["elevacao_enrol_atual"]
    theta_enrol_final = enrol_final["elevacao_enrol_atual"]
    
    # Gera pontos de tempo
    tempo = np.arange(0, tempo_total + intervalo, intervalo)
    
    # Calcula curvas de temperatura
    # θ(t) = θ_final - (θ_final - θ_inicial) * e^(-t/τ)
    theta_oleo = [theta_oleo_final - (theta_oleo_final - theta_oleo_inicial) * math.exp(-t / tau_oleo) for t in tempo]
    theta_enrol = [theta_enrol_final - (theta_enrol_final - theta_enrol_inicial) * math.exp(-t / tau_enrol) for t in tempo]
    
    # Temperaturas absolutas (°C)
    temp_oleo = [temp_ambiente + theta for theta in theta_oleo]
    temp_enrol = [temp_ambiente + theta for theta in theta_enrol]
    
    return {
        "tempo": tempo.tolist(),
        "elevacao_oleo": theta_oleo,
        "elevacao_enrol": theta_enrol,
        "temp_oleo": temp_oleo,
        "temp_enrol": temp_enrol,
        "temp_ambiente": temp_ambiente
    }


def calculate_temperature_analysis(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Realiza a análise completa de elevação de temperatura para um transformador.
    
    Args:
        data: Dicionário com os parâmetros do transformador
        
    Returns:
        Dicionário com todos os resultados calculados
    """
    # Cálculos de elevação de temperatura
    oleo = calculate_oil_temperature_rise(data)
    enrol = calculate_winding_temperature_rise(data)
    
    # Temperatura ambiente de referência
    temp_ambiente = data.get("temp_ambiente", const.TEMP_AMBIENTE_REFERENCIA)
    
    # Temperaturas absolutas (°C)
    temp_oleo = temp_ambiente + oleo["elevacao_oleo_atual"]
    temp_enrol = temp_ambiente + enrol["elevacao_enrol_atual"]
    
    # Calculando o hot-spot (ponto mais quente)
    fator_hot_spot = data.get("fator_hot_spot", 1.1)
    temp_hot_spot = temp_ambiente + oleo["elevacao_oleo_atual"] + (fator_hot_spot * enrol["gradiente_atual"])
    
    # Calcular curva de temperatura
    curva_temp = calculate_temperature_time_curve(data)
    
    # Consolida os resultados
    results = {
        # Elevações de temperatura
        "elevacao_oleo_nominal": oleo["elevacao_oleo_nominal"],
        "elevacao_oleo_atual": oleo["elevacao_oleo_atual"],
        "gradiente_nominal": enrol["gradiente_nominal"],
        "gradiente_atual": enrol["gradiente_atual"],
        "elevacao_enrol_atual": enrol["elevacao_enrol_atual"],
        
        # Constantes de tempo
        "constante_tempo_oleo": oleo["constante_tempo_oleo"],
        "constante_tempo_enrol": enrol["constante_tempo_enrol"],
        
        # Temperaturas absolutas
        "temp_ambiente": temp_ambiente,
        "temp_oleo": temp_oleo,
        "temp_enrol": temp_enrol,
        "temp_hot_spot": temp_hot_spot,
        
        # Fator de envelhecimento
        "fator_envelhecimento": calculate_aging_factor(temp_hot_spot),
        
        # Curvas de temperatura
        "curva_temperatura": curva_temp
    }
    
    return results


def calculate_aging_factor(temp_hot_spot: float) -> float:
    """
    Calcula o fator de envelhecimento conforme IEC 60076-7.
    
    Args:
        temp_hot_spot: Temperatura do ponto mais quente em °C
        
    Returns:
        Fator de envelhecimento
    """
    # Constantes para papel termoestabilizado
    if temp_hot_spot <= 110:
        return math.exp((15000 / 383) - (15000 / (temp_hot_spot + 273)))
    else:
        return 1.0