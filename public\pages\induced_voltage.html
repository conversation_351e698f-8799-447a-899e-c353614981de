<!-- public/pages/induced_voltage.html -->
<div class="container-fluid ps-2 pe-0"> <!-- MODIFICADO: Adicionado ps-2 pe-0 para alinhar com a sidebar -->
    <!-- Div onde o painel de informações do transformador será injetado -->
    <div id="transformer-info-induced_voltage-page" class="mb-2"></div>
    <!-- Divs ocultas para compatibilidade global -->

    <!-- Título principal do módulo -->    <div class="card">
        <div class="card-header d-flex align-items-center">
            <h6 class="text-center m-0 flex-grow-1" style="font-size: 1.1rem; font-weight: bold; color: var(--text-header);">ANÁLISE DE TENSÃO INDUZIDA</h6>
            <button type="button" class="btn btn-sm btn-outline-light ms-2" title="Ajuda sobre Tensão Induzida">
                <i class="fas fa-question-circle"></i>
            </button>
        </div>
        <div class="card-body" id="induced-voltage-form">
            <!-- Parâmetros de Entrada -->
            <div class="row g-2 mb-2">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0" style="font-size: 1rem; font-weight: bold; color: var(--text-header);">Parâmetros de Entrada do Ensaio Tensão Induzida</h6>
                        </div>
                        <div class="card-body p-3">
                            <div class="alert alert-info py-2 px-3 mb-0" style="font-size: 0.75rem; display: flex; align-items: center;">
                                Cálculos baseados na NBR 5356-3 / IEC 60076-3 e estimativas com tabelas de aço M4.
                            </div>
                            <div class="row g-2 align-items-center justify-content-between mt-2">
                                <div class="col-md-2">
                                    <label for="tipo-transformador-induced" class="form-label text-end" style="white-space: nowrap;">Tipo:</label>
                                    <select class="form-select form-select-sm dark-dropdown" id="tipo-transformador-induced">
                                        <option value="Monofásico">Monofásico</option>
                                        <option value="Trifásico" selected>Trifásico</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="frequencia-teste" class="form-label text-end">Teste (fp):</label>
                                    <input type="number" class="form-control form-control-sm" id="frequencia-teste" placeholder="Ex: 120" value="120">
                                </div>
                                <div class="col-md-3">
                                    <label for="capacitancia" class="form-label text-end">Cap. AT-GND (pF):</label>
                                    <input type="number" class="form-control form-control-sm" id="capacitancia" placeholder="Cp AT-GND" min="0" step="1">
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button class="btn btn-primary w-100 btn-sm" id="calc-induced-voltage-btn">Calcular</button>
                                </div>
                            </div>
                            <div id="induced-voltage-error-message" class="mt-2 text-danger small"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Resultados -->
            <div class="row g-2 mb-2">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0" style="font-size: 1rem; font-weight: bold; color: var(--text-header);">Resultados Calculados</h6>
                        </div>
                        <div class="card-body">
                            <div id="resultado-tensao-induzida" class="text-muted text-center py-3">Aguardando cálculo...</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Botões para tabela de frequências -->
            <div class="row g-2 mb-2">
                <div class="col-md-12 d-flex justify-content-center">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-info btn-sm mt-3 mb-2" id="generate-frequency-table-button">Gerar Tabela de Frequências (100-240Hz)</button>
                        <button type="button" class="btn btn-secondary btn-sm mt-3 mb-2" id="clear-frequency-table-button">Limpar Tabela</button>
                    </div>
                </div>
            </div>
            <!-- Contêiner para a tabela de frequências -->
            <div class="row g-2">
                <div class="col-md-12">
                    <div id="frequency-table-container"></div>
                </div>
            </div>
        </div>
    </div>
</div>