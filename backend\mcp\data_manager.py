# backend/mcp/data_manager.py
"""
MCP Data Manager - Gerenciador central de dados da aplicação TTS.
Implementa o padrão Single Source of Truth com persistência e propagação de dados.
"""

import json
import os
import sqlite3
from typing import Dict, Any, Optional
from datetime import datetime
import threading


class MCPDataManager:
    """
    Memory Cache Proxy - Gerenciador central de dados da aplicação.
    
    Responsabilidades:
    - Manter estado da aplicação em memória
    - Gerenciar persistência no banco de dados
    - Propagar mudanças entre módulos relacionados
    - Fornecer API para leitura/escrita de dados
    """
    
    def __init__(self, db_path: str = "tts_data.db"):
        self.db_path = db_path
        self._memory_store: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        
        # Stores definidos na aplicação
        self.store_definitions = {
            'transformerInputs': {  # Nome usado no frontend
                'key': 'transformerInputs',
                'dependencies': [],  # Outros stores que devem ser atualizados quando este mudar
                'propagates_to': ['losses', 'impulse', 'appliedVoltage', 'inducedVoltage', 'shortCircuit']
            },
            'losses': {
                'key': 'losses',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            },
            'impulse': {
                'key': 'impulse',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            },
            'appliedVoltage': {  # Nome usado no frontend
                'key': 'appliedVoltage',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            },
            'inducedVoltage': {  # Nome usado no frontend
                'key': 'inducedVoltage',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            },
            'shortCircuit': {  # Nome usado no frontend
                'key': 'shortCircuit',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            },
            'temperatureRise': {  # Nome usado no frontend
                'key': 'temperatureRise',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            },
            'dielectricAnalysis': {  # Nome usado no frontend
                'key': 'dielectricAnalysis',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            },
            'standards': {
                'key': 'standards',
                'dependencies': [],
                'propagates_to': []
            },
            'sessions': {  # Para histórico
                'key': 'sessions',
                'dependencies': [],
                'propagates_to': []
            },
            'globalInfo': {  # Nome usado no frontend
                'key': 'globalInfo',
                'dependencies': ['transformerInputs'],
                'propagates_to': []
            }
        }
        
        self._init_database()
        self._load_all_stores()
    
    def _init_database(self):
        """Inicializa o banco de dados SQLite com as tabelas necessárias."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Tabela principal para armazenar os dados dos stores
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS data_stores (
                    store_id TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    version INTEGER DEFAULT 1
                )
            ''')
            
            # Tabela para histórico de sessões
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    session_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            ''')
            
            conn.commit()
    
    def _load_all_stores(self):
        """Carrega todos os stores do banco de dados para a memória."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT store_id, data FROM data_stores')
            
            for store_id, data_json in cursor.fetchall():
                try:
                    self._memory_store[store_id] = json.loads(data_json)
                except json.JSONDecodeError:
                    print(f"Erro ao carregar dados do store '{store_id}'. Inicializando vazio.")
                    self._memory_store[store_id] = {}
            
            # Inicializa stores vazios para os que não existem no DB
            for store_id in self.store_definitions:
                if store_id not in self._memory_store:
                    self._memory_store[store_id] = {}
    
    def get_data(self, store_id: str) -> Dict[str, Any]:
        """
        Obtém os dados de um store específico.
        
        Args:
            store_id: Identificador do store
            
        Returns:
            Dicionário com os dados do store
        """
        with self._lock:
            if store_id not in self.store_definitions:
                raise ValueError(f"Store '{store_id}' não existe.")
            
            return self._memory_store.get(store_id, {}).copy()
    
    def set_data(self, store_id: str, data: Dict[str, Any]) -> bool:
        """
        Define os dados completos de um store e persiste no banco.
        
        Args:
            store_id: Identificador do store
            data: Dados a serem armazenados
            
        Returns:
            True se a operação foi bem-sucedida
        """
        with self._lock:
            if store_id not in self.store_definitions:
                raise ValueError(f"Store '{store_id}' não existe.")
            
            # Atualiza na memória
            self._memory_store[store_id] = data.copy()
            
            # Persiste no banco
            self._persist_store(store_id)
            
            # Propaga mudanças se necessário
            self._propagate_changes(store_id)
            
            return True
    
    def patch_data(self, store_id: str, partial_data: Dict[str, Any]) -> bool:
        """
        Atualiza parcialmente os dados de um store.
        
        Args:
            store_id: Identificador do store
            partial_data: Dados parciais para atualização
            
        Returns:
            True se a operação foi bem-sucedida
        """
        with self._lock:
            if store_id not in self.store_definitions:
                raise ValueError(f"Store '{store_id}' não existe.")
            
            # Obtém dados atuais
            current_data = self._memory_store.get(store_id, {})
            
            # Atualiza com os novos dados
            current_data.update(partial_data)
            
            # Salva os dados atualizados
            return self.set_data(store_id, current_data)
    
    def _persist_store(self, store_id: str):
        """Persiste um store específico no banco de dados."""
        data_json = json.dumps(self._memory_store.get(store_id, {}))
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO data_stores (store_id, data, last_updated, version)
                VALUES (?, ?, CURRENT_TIMESTAMP, 
                    COALESCE((SELECT version + 1 FROM data_stores WHERE store_id = ?), 1))
            ''', (store_id, data_json, store_id))
            conn.commit()
    
    def _propagate_changes(self, store_id: str):
        """
        Propaga mudanças para stores dependentes.
        Chamado automaticamente quando dados fundamentais são alterados.
        """
        store_def = self.store_definitions.get(store_id, {})
        propagates_to = store_def.get('propagates_to', [])
        
        if not propagates_to:
            return
        
        # Se transformerInputs foi alterado, atualiza o globalInfo
        if store_id == 'transformerInputs':
            self._update_global_info()
        
        # Aqui podemos adicionar outras lógicas de propagação conforme necessário
        print(f"Propagando mudanças de '{store_id}' para: {propagates_to}")
    
    def _update_global_info(self):
        """Atualiza o store globalInfo baseado nos dados do transformerInputs."""
        transformer_data = self._memory_store.get('transformerInputs', {})
        
        # Extrai informações essenciais para o painel global
        global_info = {
            'potencia_nominal': transformer_data.get('potencia_nominal', ''),
            'tensao_primaria': transformer_data.get('tensao_primaria', ''),
            'tensao_secundaria': transformer_data.get('tensao_secundaria', ''),
            'frequencia': transformer_data.get('frequencia', ''),
            'grupo_ligacao': transformer_data.get('grupo_ligacao', ''),
            'tipo_resfriamento': transformer_data.get('tipo_resfriamento', ''),
            'corrente_nominal_primaria': transformer_data.get('corrente_nominal_primaria', ''),
            'corrente_nominal_secundaria': transformer_data.get('corrente_nominal_secundaria', ''),
            'last_updated': datetime.now().isoformat()
        }
        
        self._memory_store['globalInfo'] = global_info
        self._persist_store('globalInfo')
    
    def get_all_stores(self) -> Dict[str, Dict[str, Any]]:
        """Retorna todos os stores em um único dicionário."""
        with self._lock:
            return {store_id: data.copy() for store_id, data in self._memory_store.items()}
    
    def clear_store(self, store_id: str) -> bool:
        """Limpa os dados de um store específico."""
        with self._lock:
            if store_id not in self.store_definitions:
                raise ValueError(f"Store '{store_id}' não existe.")
            
            self._memory_store[store_id] = {}
            self._persist_store(store_id)
            return True
    
    def clear_all_stores(self) -> bool:
        """Limpa todos os stores."""
        with self._lock:
            for store_id in self.store_definitions:
                self._memory_store[store_id] = {}
                self._persist_store(store_id)
            return True
    
    def save_session(self, session_id: str, description: str = "") -> bool:
        """Salva o estado atual como uma sessão nomeada."""
        session_data = {
            'stores': self.get_all_stores(),
            'timestamp': datetime.now().isoformat()
        }
        
        session_json = json.dumps(session_data)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO sessions (session_id, session_data, created_at, description)
                VALUES (?, ?, CURRENT_TIMESTAMP, ?)
            ''', (session_id, session_json, description))
            conn.commit()
        
        return True
    
    def load_session(self, session_id: str) -> bool:
        """Carrega uma sessão específica."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT session_data FROM sessions WHERE session_id = ?', (session_id,))
            result = cursor.fetchone()
            
            if not result:
                return False
            
            try:
                session_data = json.loads(result[0])
                stores = session_data.get('stores', {})
                
                # Carrega todos os stores da sessão
                for store_id, data in stores.items():
                    if store_id in self.store_definitions:
                        self.set_data(store_id, data)
                
                return True
            except json.JSONDecodeError:
                return False
    
    def list_sessions(self) -> list:
        """Lista todas as sessões salvas."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT session_id, created_at, description 
                FROM sessions 
                ORDER BY created_at DESC
            ''')
            
            return [
                {
                    'session_id': row[0],
                    'created_at': row[1],
                    'description': row[2]
                }
                for row in cursor.fetchall()
            ]
