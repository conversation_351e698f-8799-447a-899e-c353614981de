# backend/routers/modules_routes.py
"""
Rotas para processamento de dados específicos dos módulos.
Implementa a arquitetura estruturada TTS conforme especificação.
"""

from fastapi import APIRouter, HTTPException, Body
from typing import Dict, Any, Optional
import logging

# Importa services específicos dos módulos
from backend.services.losses_service import LossesService
from backend.services.impulse_service import ImpulseService
from backend.services.applied_voltage_service import AppliedVoltageService
from backend.services.induced_voltage_service import InducedVoltageService
from backend.services.short_circuit_service import ShortCircuitService
from backend.services.temperature_rise_service import TemperatureRiseService
from backend.services.dielectric_analysis_service import DielectricAnalysisService

# Importa MCP Data Manager
from backend.mcp.data_manager import MCPDataManager

router = APIRouter(prefix="/modules", tags=["modules"])
logger = logging.getLogger(__name__)

# Instância do MCP Data Manager
mcp_data_manager = MCPDataManager()

# Mapeamento de módulos para services
MODULE_SERVICES = {
    'losses': LossesService(),
    'impulse': ImpulseService(),
    'appliedVoltage': AppliedVoltageService(),
    'inducedVoltage': InducedVoltageService(),
    'shortCircuit': ShortCircuitService(),
    'temperatureRise': TemperatureRiseService(),
    'dielectricAnalysis': DielectricAnalysisService()
}

@router.post("/{module_id}/process")
async def process_module_data(
    module_id: str,
    data: Dict[str, Any] = Body(...)
):
    """
    Processa dados específicos de um módulo.
    
    Fluxo:
    1. Recebe inputs específicos do módulo + dados básicos
    2. Chama service específico para processamento
    3. Armazena resultados no MCP
    4. Retorna dados processados
    """
    try:
        logger.info(f"Processando dados do módulo: {module_id}")
        
        # Valida se o módulo existe
        if module_id not in MODULE_SERVICES:
            raise HTTPException(
                status_code=404, 
                detail=f"Módulo '{module_id}' não encontrado"
            )
        
        # Extrai dados básicos e dados específicos do módulo
        basic_data = data.get('basicData', {})
        module_data = data.get('moduleData', {})
        
        logger.info(f"Dados básicos: {len(basic_data)} campos")
        logger.info(f"Dados do módulo: {len(module_data)} campos")
        
        # Obtém o service específico do módulo
        service = MODULE_SERVICES[module_id]
        
        # Processa os dados via service
        processed_data = await service.process_data(
            basic_data=basic_data,
            module_data=module_data
        )
        
        # Armazena no MCP
        store_data = {
            'inputs': module_data,
            'basicData': basic_data,
            'results': processed_data,
            'lastUpdated': datetime.now().isoformat()
        }
        
        success = mcp_data_manager.patch_data(module_id, store_data)
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail=f"Erro ao armazenar dados do módulo {module_id}"
            )
        
        logger.info(f"Módulo {module_id} processado com sucesso")
        
        return {
            'success': True,
            'module': module_id,
            'results': processed_data,
            'message': f'Dados do módulo {module_id} processados com sucesso'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao processar módulo {module_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno ao processar módulo {module_id}: {str(e)}"
        )

@router.get("/{module_id}/results")
async def get_module_results(module_id: str):
    """
    Obtém os resultados processados de um módulo.
    """
    try:
        if module_id not in MODULE_SERVICES:
            raise HTTPException(
                status_code=404,
                detail=f"Módulo '{module_id}' não encontrado"
            )
        
        # Obtém dados do MCP
        data = mcp_data_manager.get_data(module_id)
        
        if not data:
            return {
                'success': True,
                'module': module_id,
                'results': {},
                'message': 'Nenhum dado processado encontrado'
            }
        
        return {
            'success': True,
            'module': module_id,
            'results': data.get('results', {}),
            'lastUpdated': data.get('lastUpdated'),
            'message': f'Resultados do módulo {module_id} obtidos com sucesso'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao obter resultados do módulo {module_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno: {str(e)}"
        )

@router.get("/")
async def list_modules():
    """
    Lista todos os módulos disponíveis.
    """
    return {
        'success': True,
        'modules': list(MODULE_SERVICES.keys()),
        'message': 'Módulos disponíveis listados com sucesso'
    }
