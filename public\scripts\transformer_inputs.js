// public/scripts/transformer_inputs.js - Simplificado
// Importações do módulo comum

import { loadAndPopulateTransformerInfo } from './common_module.js';
import { setupApiFormPersistence } from './api_persistence.js';
import { initializeIsolationDropdowns, populateIsolationDropdowns, toggleNeutralFieldsVisibility } from './insulation_levels.js';

// Função para preencher os campos de corrente nominal a partir do backend
async function fillNominalCurrentsFromStore() {
    try {
        console.log('[fillNominalCurrentsFromStore] Iniciando...');
        
        if (!window.apiDataSystem) {
            console.error('[fillNominalCurrentsFromStore] apiDataSystem não disponível');
            return;
        }
        
        const store = window.apiDataSystem.getStore('transformerInputs');
        if (!store) {
            console.error('[fillNominalCurrentsFromStore] Store não encontrado');
            return;
        }
        
        const data = await store.getData();
        console.log('[fillNominalCurrentsFromStore] Dados recebidos:', data);
        
        // Verifica se os dados estão no nível raiz ou em formData
        let currentData = data || {};
        if (data && data.formData && Object.keys(data.formData).length > 0) {
            console.log('[fillNominalCurrentsFromStore] Usando dados de formData');
            currentData = { ...data, ...data.formData };
        }
        
        console.log('[fillNominalCurrentsFromStore] Dados processados:', currentData);
        
        // AT
        const correnteAT = document.getElementById('corrente_nominal_at');
        if (correnteAT) {
            const valor = currentData.corrente_nominal_at ?? '';
            correnteAT.value = valor;
            console.log('[fillNominalCurrentsFromStore] AT:', valor);
        }
        
        // BT
        const correnteBT = document.getElementById('corrente_nominal_bt');
        if (correnteBT) {
            const valor = currentData.corrente_nominal_bt ?? '';
            correnteBT.value = valor;
            console.log('[fillNominalCurrentsFromStore] BT:', valor);
        }
        
        // Terciário
        const correnteTer = document.getElementById('corrente_nominal_terciario');
        if (correnteTer) {
            const valor = currentData.corrente_nominal_terciario ?? '';
            correnteTer.value = valor;
            console.log('[fillNominalCurrentsFromStore] Terciário:', valor);
        }
        
        // Taps AT
        const correnteATTapMaior = document.getElementById('corrente_nominal_at_tap_maior');
        if (correnteATTapMaior) {
            const valor = currentData.corrente_nominal_at_tap_maior ?? '';
            correnteATTapMaior.value = valor;
            console.log('[fillNominalCurrentsFromStore] Tap Maior:', valor);
        }
        
        const correnteATTapMenor = document.getElementById('corrente_nominal_at_tap_menor');
        if (correnteATTapMenor) {
            const valor = currentData.corrente_nominal_at_tap_menor ?? '';
            correnteATTapMenor.value = valor;
            console.log('[fillNominalCurrentsFromStore] Tap Menor:', valor);
        }
        
        console.log('[fillNominalCurrentsFromStore] Concluído');
        
    } catch (error) {
        console.error('[fillNominalCurrentsFromStore] Erro:', error);
    }
}

// Atualiza correntes nominais sempre que campos relevantes mudam
function setupNominalCurrentAutoUpdate() {
    const ids = [
        'potencia_mva', 'tensao_at', 'tensao_bt', 'tensao_terciario', 'tipo_transformador',
        'tensao_at_tap_maior', 'tensao_at_tap_menor'
    ];
    
    let updateTimeout;
    
    ids.forEach(id => {
        const el = document.getElementById(id);
        if (el) {
            el.addEventListener('change', async () => {
                console.log(`[setupNominalCurrentAutoUpdate] Campo alterado: ${id}`);
                
                // Cancela timeout anterior se existir
                if (updateTimeout) {
                    clearTimeout(updateTimeout);
                }
                
                // Aguarda um tempo para a persistência automática e cálculo do backend
                updateTimeout = setTimeout(async () => {
                    console.log('[setupNominalCurrentAutoUpdate] Atualizando correntes...');
                    await fillNominalCurrentsFromStore();
                }, 1000); // Aumentei para 1 segundo
            });
        }
    });
}

// Função de inicialização do módulo Dados Básicos
async function initTransformerInputs() {
    // Preenche o painel de informações do transformador
    await loadAndPopulateTransformerInfo('transformer-info-transformer_inputs-page');

    // Configura persistência via backend e localStorage
    await setupApiFormPersistence('transformer-inputs-form-container', 'transformerInputs');

    // Preenche as correntes nominais calculadas
    await fillNominalCurrentsFromStore();

    // Configura atualização automática das correntes nominais
    setupNominalCurrentAutoUpdate();

    // Inicializa e configura os dropdowns de níveis de isolamento
    await initializeIsolationDropdowns();

    // Configura listeners para a visibilidade dos campos de neutro
    const enrolamentoPrefixos = ['at', 'bt', 'terciario'];
    enrolamentoPrefixos.forEach(prefixo => {
        const conexaoDropdown = document.getElementById(`conexao_${prefixo}`);
        if (conexaoDropdown) {
            conexaoDropdown.addEventListener('change', (event) => {
                toggleNeutralFieldsVisibility(prefixo, event.target.value);
            });
            // Garante que a visibilidade inicial seja correta ao carregar a página
            toggleNeutralFieldsVisibility(prefixo, conexaoDropdown.value);
        }
    });
}

// SPA routing: executa quando o módulo transformer_inputs é carregado
document.addEventListener('moduleContentLoaded', (event) => {
    if (event.detail && event.detail.moduleName === 'transformer_inputs') {
        console.log('[transformer_inputs] SPA routing init');
        initTransformerInputs();
    }
});
