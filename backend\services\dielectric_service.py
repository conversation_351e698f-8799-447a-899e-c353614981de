"""
Serviço para cálculos de análise dielétrica
Implementa os algoritmos descritos em docs/instrucoes_dieletrica.md
"""

import sys
import pathlib
from typing import Dict, Any, Optional, Union, List
import math
import logging

# Ajusta o path para permitir importações corretas
current_file = pathlib.Path(__file__).absolute()
current_dir = current_file.parent
backend_dir = current_dir.parent
root_dir = backend_dir.parent

# Adiciona os diretórios ao path se ainda não estiverem lá
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Tenta importar constantes para o serviço
try:
    from ..utils import constants as const
except ImportError:
    try:
        from backend.utils import constants as const
    except ImportError:
        try:
            from utils import constants as const
        except ImportError:
            logging.warning("Não foi possível importar 'constants'. Usando mock para constantes.")
            class MockConstants:
                EPSILON = 1e-6
                # Rigidez dielétrica de diferentes materiais em kV/mm
                RIGIDEZ_OLEO_MINERAL = 12.5  # média entre 10-15 kV/mm
                RIGIDEZ_PAPEL_IMPREGNADO = 50.0  # média entre 40-60 kV/mm
                RIGIDEZ_AR_NIVEL_MAR = 3.0  # kV/mm
                RIGIDEZ_SF6 = 7.5  # média entre 7-8 kV/mm
                # Constante para correção de altitude
                ALTITUDE_CONST = 8150  # metros
                
            const = MockConstants()


def calculate_altitude_correction(altitude: float) -> float:
    """
    Calcula o fator de correção para altitude conforme seção 2.2 da documentação.
    
    Args:
        altitude: Altitude em metros
    
    Returns:
        Fator de correção para altitude
    """
    return math.exp(-altitude / const.ALTITUDE_CONST)


def calculate_min_isolation_distance(voltage: float, material: str, altitude: float = 0) -> Dict[str, float]:
    """
    Calcula a distância mínima de isolamento conforme seção 2.3 da documentação.
    
    Args:
        voltage: Tensão máxima em kV
        material: Material isolante ('oleo', 'papel', 'ar', 'sf6')
        altitude: Altitude em metros (relevante apenas para o ar)
    
    Returns:
        Dicionário com distância mínima e rigidez dielétrica aplicada
    """
    # Seleciona a rigidez dielétrica base de acordo com o material
    if material.lower() == 'oleo':
        rigidez = const.RIGIDEZ_OLEO_MINERAL
    elif material.lower() == 'papel':
        rigidez = const.RIGIDEZ_PAPEL_IMPREGNADO
    elif material.lower() == 'sf6':
        rigidez = const.RIGIDEZ_SF6
    else:  # ar como padrão
        rigidez = const.RIGIDEZ_AR_NIVEL_MAR
        # Aplica correção para altitude se for ar
        if altitude > 0:
            rigidez *= calculate_altitude_correction(altitude)
    
    # Calcula a distância mínima
    distancia_min = voltage / rigidez
    
    return {
        "distancia_minima": distancia_min,
        "rigidez_dieletrica": rigidez
    }


def analyze_dielectric_strength(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Realiza a análise dielétrica completa com base nos parâmetros do transformador.
    
    Args:
        data: Dicionário com os parâmetros do transformador
    
    Returns:
        Dicionário com os resultados da análise dielétrica
    """
    # Extrai parâmetros básicos
    tipo_transformador = data.get("tipo_transformador", "Trifásico")
    tensao_at = data.get("tensao_at", 0)
    tensao_bt = data.get("tensao_bt", 0)
    classe_tensao_at = data.get("classe_tensao_at", 0)
    classe_tensao_bt = data.get("classe_tensao_bt", 0)
    bil = data.get("nbi_at", 0)  # Nível Básico de Isolamento para AT
    ac_level = data.get("teste_tensao_aplicada_at", 0)  # Nível de isolamento AC para AT
    espacamentos = data.get("espacamentos", {})
    meio_isolante = data.get("liquido_isolante", "óleo mineral")
    altitude = data.get("altitude", 1000)  # Altitude em metros, padrão 1000m
    
    # Determine material isolante baseado no meio_isolante
    if "óleo" in meio_isolante.lower() or "oleo" in meio_isolante.lower():
        material = "oleo"
    else:
        material = "ar"
    
    # Cálculos para AT (BIL)
    resultado_bil_at = calculate_min_isolation_distance(bil, material, altitude)
    
    # Cálculos para AT (AC)
    resultado_ac_at = calculate_min_isolation_distance(ac_level, material, altitude)
    
    # Cálculos para BT (BIL)
    bil_bt = data.get("nbi_bt", 0)
    resultado_bil_bt = calculate_min_isolation_distance(bil_bt, material, altitude) if bil_bt > 0 else None
    
    # Cálculos para BT (AC)
    ac_level_bt = data.get("teste_tensao_aplicada_bt", 0)
    resultado_ac_bt = calculate_min_isolation_distance(ac_level_bt, material, altitude) if ac_level_bt > 0 else None
    
    # Fator de correção para altitude
    fator_correcao_altitude = calculate_altitude_correction(altitude)
    
    # Prepara resultados
    results = {
        # Parâmetros de entrada relevantes
        "material_isolante": material,
        "altitude": altitude,
        "fator_correcao_altitude": fator_correcao_altitude,
        
        # Resultados para AT
        "at_bil_distancia_minima": resultado_bil_at["distancia_minima"],
        "at_bil_rigidez_dieletrica": resultado_bil_at["rigidez_dieletrica"],
        "at_ac_distancia_minima": resultado_ac_at["distancia_minima"],
        "at_ac_rigidez_dieletrica": resultado_ac_at["rigidez_dieletrica"],
        
        # Resultados para BT (se aplicável)
        "bt_bil_distancia_minima": resultado_bil_bt["distancia_minima"] if resultado_bil_bt else None,
        "bt_bil_rigidez_dieletrica": resultado_bil_bt["rigidez_dieletrica"] if resultado_bil_bt else None,
        "bt_ac_distancia_minima": resultado_ac_bt["distancia_minima"] if resultado_ac_bt else None,
        "bt_ac_rigidez_dieletrica": resultado_ac_bt["rigidez_dieletrica"] if resultado_ac_bt else None,
    }
    
    return results