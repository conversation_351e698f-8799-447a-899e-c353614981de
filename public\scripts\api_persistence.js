// public/scripts/api_persistence.js
// Sistema de persistência via API REST para o TTS

// Sistema global de gerenciamento de dados
const apiDataSystem = {
    baseURL: 'http://localhost:8000/api/data',
    stores: new Map(),
    initialized: false,

    // Inicializa o sistema
    async init() {
        if (this.initialized) return;

        try {
            // Testa conectividade com backend
            const response = await fetch(`${this.baseURL}/health`);
            if (!response.ok) {
                console.warn('[apiDataSystem] Backend não disponível, usando fallback localStorage');
                this.useLocalStorageFallback = true;
            }
            this.initialized = true;
            console.log('[apiDataSystem] Sistema inicializado');
        } catch (error) {
            console.warn('[apiDataSystem] Erro ao conectar com backend:', error);
            this.useLocalStorageFallback = true;
            this.initialized = true;
        }
    },

    // Obtém um store específico
    getStore(storeId) {
        if (!this.stores.has(storeId)) {
            this.stores.set(storeId, new DataStore(storeId, this));
        }
        return this.stores.get(storeId);
    }
};

// Classe para gerenciar um store individual
class DataStore {
    constructor(storeId, apiSystem) {
        this.storeId = storeId;
        this.apiSystem = apiSystem;
        this.cache = null;
        this.lastFetch = 0;
        this.cacheTimeout = 5000; // 5 segundos
    }

    // Carrega dados do store
    async getData() {
        await this.apiSystem.init();

        // Verifica cache
        const now = Date.now();
        if (this.cache && (now - this.lastFetch) < this.cacheTimeout) {
            return this.cache;
        }

        try {
            if (this.apiSystem.useLocalStorageFallback) {
                // Fallback para localStorage
                const data = localStorage.getItem(`store_${this.storeId}`);
                this.cache = data ? JSON.parse(data) : {};
            } else {
                // Busca do backend
                const response = await fetch(`${this.apiSystem.baseURL}/stores/${this.storeId}`);
                if (response.ok) {
                    this.cache = await response.json();
                } else {
                    console.warn(`[DataStore] Erro ao carregar store ${this.storeId}:`, response.status);
                    this.cache = {};
                }
            }

            this.lastFetch = now;
            return this.cache;
        } catch (error) {
            console.error(`[DataStore] Erro ao carregar dados do store ${this.storeId}:`, error);
            return {};
        }
    }

    // Atualiza dados do store
    async updateData(newData) {
        await this.apiSystem.init();

        try {
            if (this.apiSystem.useLocalStorageFallback) {
                // Fallback para localStorage
                const currentData = await this.getData();
                const updatedData = { ...currentData, ...newData };
                localStorage.setItem(`store_${this.storeId}`, JSON.stringify(updatedData));
                this.cache = updatedData;
            } else {
                // Envia para backend
                const response = await fetch(`${this.apiSystem.baseURL}/stores/${this.storeId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(newData)
                });

                if (response.ok) {
                    this.cache = await response.json();
                } else {
                    console.error(`[DataStore] Erro ao salvar store ${this.storeId}:`, response.status);
                    console.log(`[DataStore] Usando localStorage como fallback para ${this.storeId}`);
                    // Fallback automático para localStorage
                    const currentData = JSON.parse(localStorage.getItem(`store_${this.storeId}`) || '{}');
                    const updatedData = { ...currentData, ...newData };
                    localStorage.setItem(`store_${this.storeId}`, JSON.stringify(updatedData));
                    this.cache = updatedData;
                }
            }

            console.log(`[DataStore] Dados atualizados no store ${this.storeId}:`, newData);
        } catch (error) {
            console.error(`[DataStore] Erro ao atualizar dados do store ${this.storeId}:`, error);
            console.log(`[DataStore] Usando localStorage como fallback de emergência para ${this.storeId}`);
            // Fallback de emergência para localStorage
            try {
                const currentData = JSON.parse(localStorage.getItem(`store_${this.storeId}`) || '{}');
                const updatedData = { ...currentData, ...newData };
                localStorage.setItem(`store_${this.storeId}`, JSON.stringify(updatedData));
                this.cache = updatedData;
                console.log(`[DataStore] Dados salvos no localStorage para ${this.storeId}`);
            } catch (localError) {
                console.error(`[DataStore] Erro crítico - nem backend nem localStorage funcionaram:`, localError);
            }
        }
    }

    // Define dados completos do store
    async setData(data) {
        await this.apiSystem.init();

        try {
            if (this.apiSystem.useLocalStorageFallback) {
                localStorage.setItem(`store_${this.storeId}`, JSON.stringify(data));
                this.cache = data;
            } else {
                const response = await fetch(`${this.apiSystem.baseURL}/stores/${this.storeId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    this.cache = await response.json();
                } else {
                    console.error(`[DataStore] Erro ao definir store ${this.storeId}:`, response.status);
                }
            }
        } catch (error) {
            console.error(`[DataStore] Erro ao definir dados do store ${this.storeId}:`, error);
        }
    }
}

// Função para configurar persistência automática em formulários
async function setupApiFormPersistence(formIdOrElement, storeId) {
    console.log(`[setupApiFormPersistence] *** INICIANDO *** ${formIdOrElement} → ${storeId}`);

    await apiDataSystem.init();
    console.log(`[setupApiFormPersistence] Sistema API inicializado`);

    let formElement;
    if (typeof formIdOrElement === 'string') {
        console.log(`[setupApiFormPersistence] Procurando formulário por ID: ${formIdOrElement}`);
        formElement = document.getElementById(formIdOrElement);
        if (!formElement) {
            console.error(`[setupApiFormPersistence] *** ERRO *** Formulário ${formIdOrElement} não encontrado`);
            console.log(`[setupApiFormPersistence] Elementos disponíveis:`, document.querySelectorAll('form, [id*="form"]'));
            return;
        }
        console.log(`[setupApiFormPersistence] *** FORMULÁRIO ENCONTRADO *** ${formIdOrElement}`);
    } else if (formIdOrElement instanceof Element) {
        formElement = formIdOrElement;
        console.log(`[setupApiFormPersistence] Usando elemento fornecido diretamente`);
    } else {
        console.warn(`[setupApiFormPersistence] Parâmetro inválido:`, formIdOrElement);
        return;
    }

    const store = apiDataSystem.getStore(storeId);

    // Carrega dados existentes
    const existingData = await store.getData();
    if (existingData && existingData.formData) {
        fillFormWithData(formElement, existingData.formData);
    }

    // Configura listeners para auto-save
    const inputs = formElement.querySelectorAll('input, select, textarea');
    console.log(`[setupApiFormPersistence] Configurando listeners para ${inputs.length} inputs`);

    inputs.forEach((input, index) => {
        console.log(`[setupApiFormPersistence] Configurando listener ${index + 1}: ${input.id || input.name || input.type}`);
        input.addEventListener('change', async () => {
            console.log(`[setupApiFormPersistence] *** MUDANÇA DETECTADA *** em ${input.id || input.name}`);
            const formData = collectFormData(formElement);
            console.log(`[setupApiFormPersistence] Dados coletados:`, formData);

            await store.updateData({ formData });
            console.log(`[setupApiFormPersistence] Dados salvos no store ${storeId}`);

            // Sistema de propagação estruturado conforme arquitetura TTS
            await handleDataPropagation(storeId, formData);
        });
    });

    const elementId = formElement.id || formElement.className || 'elemento-sem-id';
    console.log(`[setupApiFormPersistence] Persistência configurada para ${elementId} → ${storeId}`);
}

// Coleta dados do formulário
function collectFormData(formElement) {
    const formData = {};
    const inputs = formElement.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        if (input.id) {
            if (input.type === 'checkbox') {
                formData[input.id] = input.checked;
            } else if (input.type === 'radio') {
                if (input.checked) {
                    formData[input.name] = input.value;
                }
            } else {
                formData[input.id] = input.value;
            }
        }
    });

    return formData;
}

// Preenche formulário com dados
function fillFormWithData(formElement, data) {
    Object.keys(data).forEach(key => {
        const element = formElement.querySelector(`#${key}`);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = data[key];
            } else if (element.type === 'radio') {
                if (element.value === data[key]) {
                    element.checked = true;
                }
            } else {
                element.value = data[key];
            }
        }
    });
}

// Sistema de propagação estruturado conforme arquitetura TTS
async function handleDataPropagation(storeId, formData) {
    console.log(`[handleDataPropagation] Iniciando propagação para ${storeId}`);

    try {
        // 1. DADOS BÁSICOS → Propagam para todos os módulos
        if (storeId === 'transformerInputs') {
            console.log('[handleDataPropagation] Dados básicos atualizados - propagando para todos os módulos');

            // Dispara evento global para atualizar templates
            document.dispatchEvent(new CustomEvent('transformerDataUpdated', {
                detail: { storeId, formData }
            }));

            // Chama services do backend para processar dados básicos
            await processBasicDataInServices(formData);
        }

        // 2. INPUTS ESPECÍFICOS → Processam via services específicos
        else {
            console.log(`[handleDataPropagation] Inputs específicos do módulo ${storeId} - processando via services`);

            // Obtém dados básicos para combinar com inputs específicos
            const basicDataStore = apiDataSystem.getStore('transformerInputs');
            const basicData = await basicDataStore.getData();

            // Chama service específico do módulo
            await processModuleDataInServices(storeId, formData, basicData?.formData);

            // Dispara evento específico do módulo
            document.dispatchEvent(new CustomEvent('moduleDataUpdated', {
                detail: { storeId, formData }
            }));
        }

        // 3. GLOBAL UPDATE → Atualiza MCP em ciclo estruturado
        await triggerGlobalUpdate(storeId);

    } catch (error) {
        console.error(`[handleDataPropagation] Erro na propagação de ${storeId}:`, error);
    }
}

// Processa dados básicos via services do backend
async function processBasicDataInServices(basicData) {
    try {
        const response = await fetch(`${apiDataSystem.baseURL}/../transformer/inputs`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(basicData)
        });

        if (response.ok) {
            const result = await response.json();
            console.log('[processBasicDataInServices] Dados básicos processados:', result);
        }
    } catch (error) {
        console.error('[processBasicDataInServices] Erro:', error);
    }
}

// Processa dados específicos do módulo via services
async function processModuleDataInServices(moduleId, moduleData, basicData) {
    try {
        const payload = {
            basicData: basicData || {},
            moduleData: moduleData
        };

        const response = await fetch(`${apiDataSystem.baseURL}/../transformer/modules/${moduleId}/process`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (response.ok) {
            const result = await response.json();
            console.log(`[processModuleDataInServices] Módulo ${moduleId} processado:`, result);
        }
    } catch (error) {
        console.error(`[processModuleDataInServices] Erro no módulo ${moduleId}:`, error);
    }
}

// Dispara atualização global do MCP
async function triggerGlobalUpdate(storeId) {
    try {
        const response = await fetch(`${apiDataSystem.baseURL}/../transformer/global-update`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ triggeredBy: storeId, timestamp: new Date().toISOString() })
        });

        if (response.ok) {
            console.log(`[triggerGlobalUpdate] Atualização global disparada por ${storeId}`);
        }
    } catch (error) {
        console.error('[triggerGlobalUpdate] Erro:', error);
    }
}

// Disponibiliza globalmente
window.apiDataSystem = apiDataSystem;
window.setupApiFormPersistence = setupApiFormPersistence;

// Exporta funções para módulos ES6
export { apiDataSystem, setupApiFormPersistence };