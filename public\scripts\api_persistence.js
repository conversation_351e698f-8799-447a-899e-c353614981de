// public/scripts/api_persistence.js
// Sistema de persistência via API REST para o TTS

// Sistema global de gerenciamento de dados
const apiDataSystem = {
    baseURL: 'http://localhost:8000/api/data',
    stores: new Map(),
    initialized: false,

    // Inicializa o sistema
    async init() {
        if (this.initialized) return;

        try {
            // Testa conectividade com backend
            const response = await fetch(`${this.baseURL}/health`);
            if (!response.ok) {
                console.warn('[apiDataSystem] Backend não disponível, usando fallback localStorage');
                this.useLocalStorageFallback = true;
            }
            this.initialized = true;
            console.log('[apiDataSystem] Sistema inicializado');
        } catch (error) {
            console.warn('[apiDataSystem] Erro ao conectar com backend:', error);
            this.useLocalStorageFallback = true;
            this.initialized = true;
        }
    },

    // Obtém um store específico
    getStore(storeId) {
        if (!this.stores.has(storeId)) {
            this.stores.set(storeId, new DataStore(storeId, this));
        }
        return this.stores.get(storeId);
    }
};

// Classe para gerenciar um store individual
class DataStore {
    constructor(storeId, apiSystem) {
        this.storeId = storeId;
        this.apiSystem = apiSystem;
        this.cache = null;
        this.lastFetch = 0;
        this.cacheTimeout = 5000; // 5 segundos
    }

    // Carrega dados do store
    async getData() {
        await this.apiSystem.init();

        // Verifica cache
        const now = Date.now();
        if (this.cache && (now - this.lastFetch) < this.cacheTimeout) {
            return this.cache;
        }

        try {
            if (this.apiSystem.useLocalStorageFallback) {
                // Fallback para localStorage
                const data = localStorage.getItem(`store_${this.storeId}`);
                this.cache = data ? JSON.parse(data) : {};
            } else {
                // Busca do backend
                const response = await fetch(`${this.apiSystem.baseURL}/stores/${this.storeId}`);
                if (response.ok) {
                    this.cache = await response.json();
                } else {
                    console.warn(`[DataStore] Erro ao carregar store ${this.storeId}:`, response.status);
                    this.cache = {};
                }
            }

            this.lastFetch = now;
            return this.cache;
        } catch (error) {
            console.error(`[DataStore] Erro ao carregar dados do store ${this.storeId}:`, error);
            return {};
        }
    }

    // Atualiza dados do store
    async updateData(newData) {
        await this.apiSystem.init();

        try {
            if (this.apiSystem.useLocalStorageFallback) {
                // Fallback para localStorage
                const currentData = await this.getData();
                const updatedData = { ...currentData, ...newData };
                localStorage.setItem(`store_${this.storeId}`, JSON.stringify(updatedData));
                this.cache = updatedData;
            } else {
                // Envia para backend
                const response = await fetch(`${this.apiSystem.baseURL}/stores/${this.storeId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(newData)
                });

                if (response.ok) {
                    this.cache = await response.json();
                } else {
                    console.error(`[DataStore] Erro ao salvar store ${this.storeId}:`, response.status);
                }
            }

            console.log(`[DataStore] Dados atualizados no store ${this.storeId}:`, newData);
        } catch (error) {
            console.error(`[DataStore] Erro ao atualizar dados do store ${this.storeId}:`, error);
        }
    }

    // Define dados completos do store
    async setData(data) {
        await this.apiSystem.init();

        try {
            if (this.apiSystem.useLocalStorageFallback) {
                localStorage.setItem(`store_${this.storeId}`, JSON.stringify(data));
                this.cache = data;
            } else {
                const response = await fetch(`${this.apiSystem.baseURL}/stores/${this.storeId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    this.cache = await response.json();
                } else {
                    console.error(`[DataStore] Erro ao definir store ${this.storeId}:`, response.status);
                }
            }
        } catch (error) {
            console.error(`[DataStore] Erro ao definir dados do store ${this.storeId}:`, error);
        }
    }
}

// Função para configurar persistência automática em formulários
async function setupApiFormPersistence(formIdOrElement, storeId) {
    await apiDataSystem.init();

    let formElement;
    if (typeof formIdOrElement === 'string') {
        formElement = document.getElementById(formIdOrElement);
        if (!formElement) {
            console.warn(`[setupApiFormPersistence] Formulário ${formIdOrElement} não encontrado`);
            return;
        }
    } else if (formIdOrElement instanceof Element) {
        formElement = formIdOrElement;
    } else {
        console.warn(`[setupApiFormPersistence] Parâmetro inválido:`, formIdOrElement);
        return;
    }

    const store = apiDataSystem.getStore(storeId);

    // Carrega dados existentes
    const existingData = await store.getData();
    if (existingData && existingData.formData) {
        fillFormWithData(formElement, existingData.formData);
    }

    // Configura listeners para auto-save
    const inputs = formElement.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('change', async () => {
            const formData = collectFormData(formElement);
            await store.updateData({ formData });
        });
    });

    const elementId = formElement.id || formElement.className || 'elemento-sem-id';
    console.log(`[setupApiFormPersistence] Persistência configurada para ${elementId} → ${storeId}`);
}

// Coleta dados do formulário
function collectFormData(formElement) {
    const formData = {};
    const inputs = formElement.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        if (input.id) {
            if (input.type === 'checkbox') {
                formData[input.id] = input.checked;
            } else if (input.type === 'radio') {
                if (input.checked) {
                    formData[input.name] = input.value;
                }
            } else {
                formData[input.id] = input.value;
            }
        }
    });

    return formData;
}

// Preenche formulário com dados
function fillFormWithData(formElement, data) {
    Object.keys(data).forEach(key => {
        const element = formElement.querySelector(`#${key}`);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = data[key];
            } else if (element.type === 'radio') {
                if (element.value === data[key]) {
                    element.checked = true;
                }
            } else {
                element.value = data[key];
            }
        }
    });
}

// Disponibiliza globalmente
window.apiDataSystem = apiDataSystem;
window.setupApiFormPersistence = setupApiFormPersistence;

// Exporta funções para módulos ES6
export { apiDataSystem, setupApiFormPersistence };