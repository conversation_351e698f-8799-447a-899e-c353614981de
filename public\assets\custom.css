/* public/assets/custom.css - Versão Completa e Detalhada */

/* Cores Base - Traduzindo de seus COLORS dict e adicionando variáveis para fácil modificação */
:root {
    /* Cores Principais */
    --primary-color: #26427A; /* Azul Escuro do Dash */
    --secondary-color: #6c757d; /* Cinza Escuro */
    --success-color: #28a745; /* Verde */
    --info-color: #17a2b8; /* Azul Claro/Ciano */
    --warning-color: #ffc107; /* Amarelo */
    --danger-color: #dc3545; /* Vermelho */
    --light-color: #f8f9fa; /* Branco Sujo */
    --dark-color: #343a40; /* Preto Quase */
    --accent-color: var(--primary-color); /* Usado como primary para destaque */
    --fail-color: var(--danger-color); /* Cor para indicar falha */

    /* Cores de Fundo e Bordas */
    --background-main: #1a1a1a; /* Fundo principal da aplicação */
    --background-card: #2c2c2c; /* Fundo dos cards e painéis */
    --background-card-header: #2a2a2a; /* Fundo do cabeçalho dos cards e sidebar */
    --background-input: #3d3d3d; /* Fundo dos campos de input e dropdowns */
    --background-card-light: #4F4F4F; /* Fundo mais claro para certas seções/tabelas (ex: Applied Voltage) */
    --background-table-row-even: #3D3D3D; /* Para linhas pares de tabelas */

    --border-color: #666666; /* Cor da borda geral */
    --border-strong: #999999; /* Cor da borda mais forte (HR) */

    /* Cores de Texto */
    --text-light: #f0f0f0; /* Texto principal claro */
    --text-dark: #333333; /* Texto escuro (para temas claros ou elementos específicos) */
    --text-header: #ffffff; /* Texto em cabeçalhos (branco puro) */
    --text-muted: #adb5bd; /* Texto secundário, atenuado */
    --text-placeholder: #adb5bd; /* Cor para placeholders de input */

    /* Tamanhos de Fonte Base - Adapte conforme a necessidade de densidade */
    --font-size-base: 0.875rem; /* ~14px */
    --font-size-sm: 0.75rem;    /* ~12px */
    --font-size-xs: 0.65rem;    /* ~10.4px */

    /* Alturas de Componentes */
    --input-height: 32px;
    --input-sm-height: 28px; /* Para inputs pequenos em Impulse */
}

/* Base Body Styles */
html, body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-main);
    color: var(--text-light);
    margin: 0;
    padding: 0;
    font-size: var(--font-size-base);
    overflow-x: hidden; /* Previne scroll horizontal */
    width: 100%;
    max-width: 100vw; /* Garante que não exceda a viewport */
    height: 100vh; /* Garante altura total da viewport */
}

/* Global Layout Fixes - Previne overflow horizontal */
* {
    box-sizing: border-box;
}

.container-fluid {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

.row {
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
}

.col, .col-*, [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    max-width: 100%;
}

/* Navbar responsiva - ajustes para telas pequenas */
@media (max-width: 991.98px) {
    .navbar-brand h4 {
        font-size: 0.95rem;
    }
    
    .navbar-brand div {
        font-size: 0.6rem;
    }
    
    .navbar .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

@media (max-width: 767.98px) {
    .navbar-brand h4 {
        font-size: 0.85rem;
    }
    
    .navbar-brand img {
        height: 35px;
        margin-right: 10px;
    }
}

/* Container de ações da navbar */
.navbar-actions-container {
    max-width: 100%;
    overflow: hidden;
    flex-shrink: 1;
}

@media (max-width: 991.98px) {
    .navbar-actions-container {
        width: 100%;
        justify-content: center;
        margin-top: 0.5rem;
        border-top: 1px solid rgba(255,255,255,0.1);
        padding-top: 0.5rem;
    }
}

/* General Link Styles */
a {
    color: var(--primary-color);
    text-decoration: none;
}
a:hover {
    color: lighten(var(--primary-color), 10%);
    text-decoration: underline;
}

/* --- Card Styles --- */
.card {
    background-color: var(--background-card);
    border: 1px solid var(--border-color);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25); /* Mais pronunciado */
    border-radius: 8px; /* Mais arredondado */
    overflow: hidden; /* Garante que o header arredondado funcione bem */
}

.card-header {
    background-color: var(--background-card-header);
    color: var(--text-header);
    padding: 0.75rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    border-radius: 7px 7px 0 0; /* Arredondado só no topo */
    font-weight: bold;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.card-body {
    padding: 1rem;
    color: var(--text-light);
}

/* Specific Card Headers for modules */
.card-header h5, .card-header h6 {
    margin: 0;
    color: inherit; /* Garante que use a cor do header */
    font-size: 1rem; /* Padrão para cabeçalhos de card */
}
/* Específico para Transformer Inputs, Impulse (title) */
.card-header.bg-primary-custom { /* Adicionar esta classe no card-header do HTML */
    background-color: var(--primary-color);
    color: var(--text-header);
}
.card-header.bg-info-custom {
    background-color: var(--info-color);
    color: var(--text-header);
}
.card-header.bg-secondary-custom {
    background-color: var(--secondary-color);
    color: var(--text-header);
}

/* --- Input and Select Styles --- */
.form-control,
.form-select,
.input-group-text {
    background-color: var(--background-input);
    color: var(--text-light);
    border: 1px solid var(--border-color);
    font-size: var(--font-size-base);
    height: var(--input-height);
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    border-radius: 4px;
}

/* Wider input fields for losses module */
.form-control.losses-input-wide,
.form-select.losses-input-wide {
    width: 50% !important; /* Make input fields take 50% width of their container */
    min-width: 120px; /* Ensure minimum width for better usability */
}

/* Pequenos inputs (para Impulse) */
.form-control.form-control-sm,
.form-select.form-select-sm,
.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text {
    height: var(--input-sm-height);
    font-size: var(--font-size-sm);
    padding: 0.25rem 0.5rem;
}


.form-control::placeholder,
.form-select::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-control:focus,
.form-select:focus {
    background-color: var(--background-input);
    color: var(--text-light);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(38, 66, 122, 0.25); /* Cor primária do Dash com 25% de opacidade */
}

/* Readonly / Disabled Inputs */
.form-control:disabled,
.form-control[readonly],
.form-select:disabled,
.form-select[readonly] {
    background-color: rgba(var(--background-input-rgb), 0.5); /* Usar RGB se disponível, senão direto */
    background-color: #333333; /* Fallback */
    color: var(--text-muted);
    opacity: 0.8;
}

/* Dark Dropdown Style */
.form-select.dark-dropdown {
    background-color: var(--background-input);
    color: var(--text-light);
    border-color: var(--border-color);
}
.form-select.dark-dropdown option {
    background-color: var(--background-input);
    color: var(--text-light);
}

/* Input Group Spinners */
.input-group-text .fas {
    cursor: pointer;
    padding: 0 5px;
    color: var(--text-muted);
    transition: color 0.15s ease-in-out;
}
.input-group-text .fas:hover {
    color: var(--info-color); /* Cor de destaque ao passar o mouse */
}

/* --- Labels --- */
label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

/* --- Button Styles (replicated from Dash) --- */
.btn {
    font-size: var(--font-size-sm);
    padding: 0.4rem 0.8rem; /* Ajustado para ser mais compacto */
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    line-height: 1.5; /* Para centralizar texto em botões menores */
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-header);
}
.btn-primary:hover {
    background-color: darken(var(--primary-color), 7%);
    border-color: darken(var(--primary-color), 7%);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-header);
}
.btn-secondary:hover {
    background-color: darken(var(--secondary-color), 7%);
    border-color: darken(var(--secondary-color), 7%);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: var(--text-header);
}
.btn-info:hover {
    background-color: darken(var(--info-color), 7%);
    border-color: darken(var(--info-color), 7%);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--text-header);
}
.btn-success:hover {
    background-color: darken(var(--success-color), 7%);
    border-color: darken(var(--success-color), 7%);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--text-header);
}
.btn-danger:hover {
    background-color: darken(var(--danger-color), 7%);
    border-color: darken(var(--danger-color), 7%);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color); /* Texto escuro para contraste no amarelo */
}
.btn-warning:hover {
    background-color: darken(var(--warning-color), 7%);
    border-color: darken(var(--warning-color), 7%);
}

.btn-outline-info { /* Para "Sugerir Resistores" */
    border-color: var(--info-color);
    color: var(--info-color);
    background-color: transparent;
}
.btn-outline-info:hover {
    background-color: var(--info-color);
    color: var(--text-header);
}

.btn-sm {
    font-size: var(--font-size-sm);
    padding: 0.25rem 0.5rem;
}

/* Helper function to darken color - used for hover effects */
/* Requires a CSS preprocessor like Sass or Less for actual darken() */
/* For pure CSS, you'd use direct rgba() or calculate manually */
/* As a fallback, I'll use fixed values for hover or simple filter:brightness() */
.btn:hover {
    filter: brightness(1.1); /* Ligeiramente mais brilhante no hover */
}
.btn-outline-info:hover {
    filter: none; /* Não aplicar brightness se a cor de fundo mudar */
}


/* --- Typography --- */
.section-title {
    font-size: var(--font-size-base); /* Ajustado para ser mais compacto */
    font-weight: bold;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 0.8rem; /* Reduzido */
    padding-bottom: 0.4rem; /* Reduzido */
    border-bottom: 1px solid rgba(var(--border-color-rgb), 0.3); /* Fallback to rgba */
    border-bottom: 1px solid rgba(102, 102, 102, 0.3); /* Direct rgba */
}

.subsection-title {
    font-size: var(--font-size-sm); /* Reduzido */
    font-weight: bold;
    color: var(--info-color);
    text-align: center;
    margin-top: 0.8rem;
    margin-bottom: 0.6rem;
    padding-bottom: 0.2rem;
    border-bottom: 1px dotted rgba(var(--border-color-rgb), 0.2); /* Fallback */
    border-bottom: 1px dotted rgba(102, 102, 102, 0.2); /* Direct rgba */
}

small.text-muted {
    font-size: var(--font-size-xs);
    color: var(--text-muted) !important;
}

/* --- Alerts and Messages --- */
.alert {
    font-size: var(--font-size-sm);
    padding: 0.6rem 1rem;
    border-radius: 5px;
    margin-bottom: 0.75rem; /* Padrão Bootstrap */
}
.alert.alert-info {
    background-color: var(--info-color);
    color: var(--text-header);
    border-color: var(--info-color);
}
.alert.alert-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
    border-color: var(--warning-color);
}
.alert.alert-danger {
    background-color: var(--danger-color);
    color: var(--text-header);
    border-color: var(--danger-color);
}
.alert.alert-success {
    background-color: var(--success-color);
    color: var(--text-header);
    border-color: var(--success-color);
}
.alert.alert-light-custom { /* Para notas informativas */
    background-color: var(--background-card-header); /* Cor do card-header para notas */
    color: var(--text-light);
    border-color: var(--border-color);
}
.alert.alert-light-custom .small {
    font-size: var(--font-size-sm);
}

/* --- Tables --- */
.table {
    --bs-table-color: var(--text-light);
    --bs-table-bg: var(--background-card);
    --bs-table-border-color: var(--border-color);
    --bs-table-striped-bg: rgba(var(--text-light-rgb), 0.05); /* Usar RGB */
    --bs-table-striped-bg: rgba(240, 240, 240, 0.05); /* Direct rgba */
    --bs-table-active-bg: rgba(var(--text-light-rgb), 0.1);
    --bs-table-active-bg: rgba(240, 240, 240, 0.1);
    --bs-table-hover-bg: rgba(var(--text-light-rgb), 0.075);
    --bs-table-hover-bg: rgba(240, 240, 240, 0.075);
    color: var(--text-light);
}

.table thead th {
    background-color: var(--background-card-header);
    color: var(--text-header);
    border-color: var(--border-color);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    padding: 0.5rem 0.75rem;
}

.table tbody td {
    font-size: var(--font-size-sm);
    padding: 0.4rem 0.75rem;
    vertical-align: middle;
}
.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--background-table-row-even); /* Cor alternativa */
}


/* --- Spacing Utilities (replicated from Dash) --- */
/* (g-x e mb-x já são do Bootstrap, mas os px-y, py-y, etc. são custom) */
.g-0 { --bs-gutter-x: 0; --bs-gutter-y: 0; }
.g-1 { --bs-gutter-x: 0.25rem; --bs-gutter-y: 0.25rem; }
.g-2 { --bs-gutter-x: 0.5rem; --bs-gutter-y: 0.5rem; }
.g-3 { --bs-gutter-x: 1rem; --bs-gutter-y: 1rem; }

.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }

.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.py-3 { padding-top: 1rem !important; padding-bottom: 1rem !important; }

.pe-1 { padding-right: 0.25rem !important; } /* Padding End */
.ps-1 { padding-left: 0.25rem !important; }  /* Padding Start */

/* Margens */
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }

/* --- Other Utilities --- */
.h-100 { height: 100% !important; }
.w-100 { width: 100% !important; }
.d-flex { display: flex !important; }
.d-inline-block { display: inline-block !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; } /* Para botões alinhados ao final da coluna */
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }
.flex-column { flex-direction: column !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.float-end { float: right !important; } /* Cuidado com floats em Flexbox/Grid */

.text-center { text-align: center !important; }
.text-end { text-align: right !important; }
.text-muted { color: var(--text-muted) !important; }
.text-primary { color: var(--primary-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }

.fw-bold { font-weight: bold !important; }
.fs-6 { font-size: 1rem !important; } /* Bootstrap 5 default */

.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-end { border-right: 1px solid var(--border-color) !important; }

/* Placeholder for Plotly graphs - to be replaced by actual graph container */
.plotly-graph-placeholder {
    background-color: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: var(--font-size-base);
    min-height: 200px;
    text-align: center;
}
/* Estilo para o container de spinners/loading de gráficos */
#loading-graph {
    min-height: 550px; /* Para acomodar os dois gráficos */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Custom Tabs (for Losses, Impulse results) */
.nav-tabs .nav-link {
    color: var(--text-muted);
    background-color: var(--background-card-header);
    border: 1px solid var(--border-color);
    border-bottom-color: var(--background-card-header); /* Para fundir com o card body */
    border-radius: 5px 5px 0 0;
    margin-right: 5px;
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
    transition: all 0.2s ease-in-out;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: var(--background-card);
    border-color: var(--border-color);
    border-bottom-color: var(--background-card); /* Para "se fundir" com o conteúdo */
    font-weight: bold;
    filter: brightness(1.0); /* Remove qualquer brilho no ativo */
}

.nav-tabs .nav-link:hover {
    color: var(--text-light); /* Ligeiramente mais claro no hover */
    filter: brightness(1.1); /* Ligeiramente mais brilhante no hover */
}

.tab-content {
    background-color: var(--background-card);
    border: 1px solid var(--border-color);
    border-top: none; /* Remover borda superior se fundir com a aba */
    border-radius: 0 0 5px 5px;
    padding: 1rem;
    height: 100%; /* Para que o conteúdo da aba preencha o espaço */
    max-height: 400px; /* Max height para tabelas rolarem */
    overflow-y: auto; /* Para rolagem em tabelas */
}

/* Specific styles for Impulse's tab-content tables */
.tab-content #waveform-analysis-table table,
.tab-content #circuit-parameters-display table,
.tab-content #energy-details-table table {
    width: 100%; /* Garante que a tabela use 100% da largura */
    border-collapse: collapse; /* Bordas colapsadas */
}

.tab-content #waveform-analysis-table table th,
.tab-content #waveform-analysis-table table td,
.tab-content #circuit-parameters-display table th,
.tab-content #circuit-parameters-display table td,
.tab-content #energy-details-table table th,
.tab-content #energy-details-table table td {
    border: 1px solid var(--border-color);
    padding: 0.4rem;
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.tab-content #waveform-analysis-table table th,
.tab-content #circuit-parameters-display table th,
.tab-content #energy-details-table table th {
    background-color: var(--background-card-header);
    color: var(--text-header);
    font-weight: bold;
    text-align: center;
}

.tab-content #waveform-analysis-table table tr:nth-child(even),
.tab-content #circuit-parameters-display table tr:nth-child(even),
.tab-content #energy-details-table table tr:nth-child(even) {
    background-color: var(--background-table-row-even);
}

.tab-content #waveform-analysis-table table tr:hover,
.tab-content #circuit-parameters-display table tr:hover,
.tab-content #energy-details-table table tr:hover {
    background-color: var(--bs-table-hover-bg); /* Do Bootstrap */
}


/* Estilos para a Navbar e Sidebar */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    border-bottom: 1px solid var(--border-color);
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden; /* Previne overflow horizontal */
}

.navbar .container-fluid {
    max-width: 100%;
    overflow-x: hidden;
    padding-left: 1rem;
    padding-right: 1rem;
}

.navbar-brand h4 {
    font-size: 1.1rem;
    letter-spacing: 0.05rem;
    text-shadow: 0px 1px 2px rgba(0,0,0,0.3);
    line-height: 1.2;
    color: var(--text-light);
}
.navbar-brand div span {
    font-size: 0.7rem;
    color: rgba(255,255,255,0.85);
    letter-spacing: 0.03rem;
}
.navbar-brand div span.fw-bold {
    font-weight: bold;
}
.navbar-brand div span.text-accent {
    color: var(--accent-color);
}

/* Navbar Action Buttons - Responsividade melhorada */
.navbar .d-flex {
    flex-wrap: wrap;
    gap: 0.25rem;
}

@media (max-width: 1199.98px) {
    #usage-counter-display {
        font-size: 0.7rem !important;
        padding: 0.2rem 0.5rem !important;
    }
    
    #usage-counter-display span {
        font-size: 0.65rem !important;
    }
    
    .btn-sm {
        font-size: 0.7rem !important;
        padding: 0.25rem 0.5rem !important;
    }
    
    .btn-sm i {
        font-size: 0.6rem !important;
    }
}

@media (max-width: 991.98px) {
    #usage-counter-display {
        display: none !important; /* Oculta em telas menores */
    }
    
    .navbar .d-flex {
        justify-content: flex-end;
        width: 100%;
        margin-top: 0.5rem;
    }
    
    .btn-sm span {
        display: none; /* Mostra apenas ícones em telas pequenas */
    }
}

@media (max-width: 767.98px) {
    #generate-report-btn,
    #global-clear-button {
        padding: 0.3rem 0.4rem !important;
        min-width: 35px;
    }
    
    #theme-toggle span {
        display: none; /* Mostra apenas ícone do tema */
    }
}

/* Sidebar Specific Styles */
.nav-link {
    cursor: pointer;
    background: transparent;
    border: none;
    text-align: left;
    display: block;
    width: 100%;
    padding: 0.5rem 0.5rem;
    margin-bottom: 3px;
    border-left: 3px solid transparent; /* Default */
    color: var(--text-light);
    font-weight: normal;
    font-size: var(--font-size-sm);
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    border-radius: 0 4px 4px 0;
}
.nav-link:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1); /* Ligeiramente mais escuro no hover */
    color: var(--primary-color);
}
.nav-link.active {
    background-color: rgba(var(--primary-color-rgb), 0.2); /* Fundo mais forte para ativo */
    border-left: 3px solid var(--primary-color); /* Linha de destaque */
    color: var(--primary-color);
    font-weight: bold;
}
.nav-link .fas {
    width: 16px;
    text-align: center;
    margin-right: 0.5rem;
}
.sidebar-container {
    height: calc(100vh - 120px); /* Altura fixa menos navbar e footer */
    overflow-y: auto;
    position: sticky;
    top: 0;
    background-color: var(--background-card-header);
    padding: 0.5rem;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-top: 0;
    margin-right: 0.5rem;
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: var(--border-color) var(--background-card-header); /* Firefox */
}
/* Chrome/Safari scrollbar */
.sidebar-container::-webkit-scrollbar {
    width: 8px;
}
.sidebar-container::-webkit-scrollbar-track {
    background: var(--background-card-header);
    border-radius: 10px;
}
.sidebar-container::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 10px;
    border: 2px solid var(--background-card-header);
}

/* Footer */
footer {
    background-color: var(--background-card-header);
    border-top: 1px solid var(--border-color);
    box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}


/* LIGHT THEME - Adicione esta classe ao body para mudar o tema */
[data-bs-theme="light"] {
    --background-main: #f0f2f5; /* Light gray background */
    --background-card: #ffffff; /* White cards */
    --background-card-header: #e9ecef; /* Lighter card headers */
    --background-input: #ffffff; /* White inputs */
    --background-card-light: #f8f9fa; /* Even lighter for specific sections */
    --background-table-row-even: #f0f0f0; /* Lighter alternating table rows */

    --text-light: #212529; /* Dark text for body */
    --text-dark: #f8f9fa; /* Light text (for elements that remain dark) */
    --text-header: #343a40; /* Dark text for headers */
    --text-muted: #6c757d; /* Standard muted text */
    --text-placeholder: #adb5bd;

    --border-color: #dee2e6; /* Lighter borders */
    --border-strong: #ced4da; /* Lighter strong borders */

    /* Buttons adjust automatically, but if you want specific light theme buttons: */
    .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); color: white; }
    .btn-outline-light { color: var(--secondary-color); border-color: var(--secondary-color); }
    .btn-outline-light:hover { background-color: var(--secondary-color); color: white; }

    .card { box-shadow: 0 2px 4px rgba(0,0,0,0.1); } /* Lighter shadow */
    .form-control, .form-select, .input-group-text {
        background-color: var(--background-input);
        color: var(--text-light);
        border: 1px solid var(--border-color);
    }
    .form-control:focus, .form-select:focus {
        box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    }
    .form-control:disabled, .form-control[readonly], .form-select:disabled, .form-select[readonly] {
        background-color: #e9ecef; /* Very light gray */
        color: var(--text-muted);
    }

    .form-select.dark-dropdown {
        background-color: var(--background-input);
        color: var(--text-light);
        border-color: var(--border-color);
    }
    .form-select.dark-dropdown option {
        background-color: var(--background-input);
        color: var(--text-light);
    }

    .nav-tabs .nav-link {
        background-color: var(--background-card-header);
        border-bottom-color: var(--background-card-header);
    }
    .nav-tabs .nav-link.active {
        background-color: var(--background-card);
        border-bottom-color: var(--background-card);
    }
    .plotly-graph-placeholder {
        background-color: var(--background-card);
    }

    /* Scrollbar for Light Theme */
    .sidebar-container::-webkit-scrollbar-track {
        background: var(--background-card-header);
    }
    .sidebar-container::-webkit-scrollbar-thumb {
        background-color: var(--border-color);
        border: 2px solid var(--background-card-header);
    }
}

/* Helper for Sass-like darken - for pure CSS this is just a placeholder */
/* Need to manually calculate darken colors or use JS to switch rgba values */
/* For now, just rely on brightness filter or fixed colors */

/* ======================================= */
/* PADRONIZAÇÃO GLOBAL DAS PÁGINAS */
/* ======================================= */

/* Container principal das páginas - padronização de altura */
.container-fluid.ps-2.pe-0,
.container-fluid.ps-2 {
    min-height: calc(100vh - 120px) !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Padronização dos cards principais de cada página */
.container-fluid > .card:first-of-type,
.container-fluid > .card.flex-grow-1 {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 180px) !important;
}

/* Padronização do card-body para usar todo o espaço disponível */
.card > .card-body {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Padronização dos títulos de páginas */
.card-header h5,
.card-header h6 {
    font-size: 1.1rem !important;
    font-weight: bold !important;
    color: var(--text-header) !important;
    margin: 0 !important;
}

/* Padronização dos subtítulos em cards internos */
.card .card-header h6 {
    font-size: 1rem !important;
}

/* Padronização de formulários para ocupar espaço disponível */
.row.g-2.flex-grow-1,
.row.g-2.mb-2.align-items-stretch.flex-grow-1,
.row.mb-2.align-items-stretch.flex-grow-1 {
    flex-grow: 1 !important;
    display: flex !important;
}

/* Padronização das colunas para altura igual */
.row.g-2 > .col-md-3,
.row.g-2 > .col-md-4,
.row.g-2 > .col-md-5,
.row.g-2 > .col-md-6,
.row.g-2 > .col-md-9 {
    display: flex !important;
    flex-direction: column !important;
}

/* Padronização dos cards internos para altura igual */
.col-md-3 > .card,
.col-md-4 > .card,
.col-md-5 > .card,
.col-md-6 > .card,
.col-md-9 > .card {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
}

/* Padronização do tamanho de fonte em labels */
label.form-label {
    font-size: var(--font-size-sm) !important;
    font-weight: 500 !important;
    color: var(--text-light) !important;
    margin-bottom: 0.25rem !important;
}

/* Padronização dos inputs */
.form-control,
.form-select {
    font-size: var(--font-size-base) !important;
    height: var(--input-height) !important;
}

.form-control-sm,
.form-select-sm {
    font-size: var(--font-size-sm) !important;
    height: var(--input-sm-height) !important;
}

/* Padronização das tabelas para usar espaço disponível sem scroll desnecessário */
.tab-content {
    max-height: none !important;
    overflow-y: visible !important;
    flex-grow: 1 !important;
}

/* Para páginas com abas, garantir que ocupem todo espaço */
.tab-content.flex-grow-1 {
    display: flex !important;
    flex-direction: column !important;
}

.tab-pane.fade.show.active.d-flex.flex-column.flex-grow-1 {
    flex-grow: 1 !important;
}

/* Padronização específica para cards de resultados */
.card.h-100.d-flex.flex-column {
    min-height: auto !important;
}

/* Garantir que elementos não criem scroll horizontal desnecessário */
.container-fluid, .row, .col, [class*="col-"] {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Padronização de botões */
.btn {
    font-size: var(--font-size-sm) !important;
    font-weight: 500 !important;
}

/* Padronização de alertas informativos */
.alert {
    font-size: var(--font-size-sm) !important;
    margin-bottom: 0.75rem !important;
}

.alert.alert-info p {
    font-size: 0.75rem !important;
    margin-bottom: 0 !important;
}

/* Padronização de textos explicativos pequenos */
.small, .text-muted {
    font-size: var(--font-size-xs) !important;
}

/* Padronização específica para Applied Voltage - tabela que pode precisar de scroll */
.table-responsive {
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* Padronização para evitar overflow em inputs pequenos */
input[style*="width: 75%"], 
input[style*="width: 50%"] {
    min-width: 80px !important;
}

/* === PADRONIZAÇÃO DE LAYOUT E TÍTULOS === */

/* Padronização de altura para containers principais */
.container-fluid {
    min-height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

/* Padronização de títulos principais dos módulos */
.card-header h5,
.card-header h6 {
    font-size: 1.1rem !important;
    font-weight: bold !important;
    color: var(--text-header) !important;
    margin: 0 !important;
}

/* Padronização de subtítulos em cards internos */
.card .card-header h6 {
    font-size: 1rem !important;
    font-weight: bold !important;
    color: var(--text-header) !important;
}

/* Garantir que cards principais ocupem toda altura disponível */
.card.flex-grow-1 {
    display: flex !important;
    flex-direction: column !important;
    min-height: 0 !important;
}

.card-body.d-flex.flex-column {
    flex-grow: 1 !important;
}

/* Padronização de rows principais para usar espaço disponível */
.row.flex-grow-1 {
    flex-grow: 1 !important;
    display: flex !important;
    flex-wrap: wrap !important;
}

/* Garantir que colunas tenham altura consistente */
.row.flex-grow-1 > [class*="col-"] {
    display: flex !important;
    flex-direction: column !important;
}

.row.flex-grow-1 > [class*="col-"] > .card {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Padronização de espaçamento entre elementos */
.row.g-2 {
    --bs-gutter-x: 0.5rem !important;
    --bs-gutter-y: 0.5rem !important;
}

/* Garantir que tab-content use espaço disponível */
.tab-content.flex-grow-1 {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

.tab-pane.flex-grow-1 {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Padronização de fontes pequenas */
.small, small {
    font-size: var(--font-size-xs) !important;
}

/* Padronização de labels */
.form-label {
    font-size: var(--font-size-sm) !important;
    font-weight: 500 !important;
    margin-bottom: 0.25rem !important;
}

/* Padronização de alertas informativos */
.alert {
    padding: 0.5rem !important;
    margin-bottom: 0.75rem !important;
    font-size: var(--font-size-sm) !important;
}

/* Garantir que páginas sem flex-grow-1 ainda preencham espaço */
.container-fluid > .card:not(.flex-grow-1) {
    min-height: calc(100vh - 180px);
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .container-fluid {
        min-height: calc(100vh - 100px);
    }
    
    .card-header h5,
    .card-header h6 {
        font-size: 1rem !important;
    }
    
    .card .card-header h6 {
        font-size: 0.9rem !important;
    }
}

/* Padronização específica para seções com abas */
.nav-tabs {
    margin-bottom: 1rem !important;
}

.nav-tabs .nav-link {
    font-size: var(--font-size-sm) !important;
    padding: 0.5rem 1rem !important;
}

/* Garantir que elementos com max-height também tenham min-height apropriada */
.tab-content[style*="max-height"] {
    min-height: 400px !important;
}